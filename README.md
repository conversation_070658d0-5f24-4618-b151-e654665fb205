# Eplan导线处理工具

## 项目概述

这是一个专门用于处理Eplan设计软件导线数据的工具集，主要功能包括导线统计、压头匹配、套管匹配、色带计算等。

## 核心功能模块

1. **main.py** - 运行主程序
2. **excel_file_selector.py** - GUI程序
3. **excel_formatter.py** - 生成表格列宽行宽自适应
4. **parallel_wire_statistics.py** - 并线表统计
5. **wire_code_matcher.py** - 编码匹配
6. **wire_count_processor.py** - 导线根数统计
7. **wire_length_processor.py** - 导线长度计算
8. **preprocess_wiring_table.py** - 导线数据预处理（包含回车符处理和设备类型智能识别）
9. **wire_diameter_matcher.py** - 导线线径匹配
10. **terminal_matching.py** - 压头匹配
11. **sleeve_matching.py** - 套管匹配
12. **sleeve_matching_fujian.py** - 福建特殊项目套管匹配
13. **color_band_processor.py** - 色带用量计算
14. **design_doc_processor.py** - GUI数据预处理
15. **logger_config.py** - 日志生成
16. **bom_processor.py** - BOM清单生成
17. **batch_processor.py** - 批量处理模块
18. **short_wire_processor.py** - 装置短接线处理
19. **decimal_calculator.py** - 使用decimal模块进行小数计算
20. **residual_manager.py** - 残值管理模块

## 四芯线（线径0.12）压头匹配逻辑

### 匹配流程

对于四芯线（线径0.12），压头匹配按以下步骤进行：

1. **数据预处理**
   - 线径0.12通过`format_diameter()`函数格式化为"0.1"（保留1位小数）
   - 提取设备类型和线材标识（如U1、I2等）

2. **接口类型匹配（Sheet1查询）**
   - 根据设备类型在Sheet1中查找匹配记录
   - 检查线材属性条件（如U/I、~U&~I等）
   - 获取接口类型（单长/单短/双长/双短）和概率值

3. **压头匹配（Sheet2查询）**
   - 并线要求：从接口类型提取（"单"或"双"）
   - 对应线径：使用格式化后的"0.1"
   - 接口类型：支持"长/短"双向匹配
   - 在Sheet2中查找同时满足这三个条件的压头记录

4. **数量计算**
   - 基础数量 = 导线根数 × 接口概率
   - 应用损耗率：基础数量 × (1 + 损耗率)
   - 向上取整得到最终压头数量

5. **结果汇总**
   - 按屏柜分组处理
   - 合并相同压头的数量
   - 生成最终的压头清单

### 重要改进：支持"长/短"双向匹配

**新增功能**：当压头匹配表Sheet2中"接口类型"字段出现"长/短"时，该压头可以同时匹配接口类型为"长"或"短"的需求。

**实现方式**：
- 新增`create_interface_type_condition()`函数
- 支持精确匹配和灵活匹配的组合
- 当目标接口类型为"长"时，同时匹配"长"和"长/短"
- 当目标接口类型为"短"时，同时匹配"短"和"长/短"

**示例**：
```
Sheet2中的压头配置：
- 压头A：接口类型="长"     → 只匹配"长"
- 压头B：接口类型="短"     → 只匹配"短"  
- 压头C：接口类型="长/短"  → 同时匹配"长"和"短"
```

这样的设计使得通用型压头能够更灵活地适应不同的接口需求，提高了匹配的准确性和实用性。

## 使用说明

1. 将Eplan导出的导线数据文件放入`input`目录
2. 运行`main.py`启动主程序
3. 按照界面提示选择相应的处理功能
4. 处理结果将保存在`output`目录中

## 日志系统

所有模块都配置了详细的日志记录，日志文件保存在`logs`目录中，便于问题排查和功能优化。
