# 导线和套管残值计算精度升级

## 概述

本次升级使用Python的`decimal`模块替代传统的`float`类型进行导线和套管的残值计算，解决了浮点数精度问题，提供更准确的计算结果。

## 主要改进

### 1. 新增decimal_calculator.py模块

- **精度设置**: 全局设置7位有效数字精度
- **精确截断**: 使用`ROUND_DOWN`模式进行向下截断
- **残值计算**: 提供专门的导线和套管残值计算函数

### 2. 核心功能函数

#### `precise_truncate_to_decimals(value, decimals)`
- 使用decimal模块进行精确的小数截断
- 避免浮点数精度问题
- 保持与现有代码的兼容性

#### `calculate_wire_residual(wire_length, loss_rate, decimals)`
- 计算导线的精确残值
- 公式：`截断值 = floor((长度 × (1 + 损耗率)) × 10^小数位数) / 10^小数位数`
- 返回：`(截断后的值, 残值)`

#### `calculate_sleeve_residual(total_length, unit_length, loss_rate, decimals)`
- 计算套管的精确残值
- 公式：`截断值 = floor(((总长度 × (1 + 损耗率)) / 单位长度) × 10^小数位数) / 10^小数位数`
- 返回：`(截断后的值, 残值)`

### 3. 修改的文件

#### bom_processor.py
- 更新`truncate_to_decimals`函数使用decimal模块
- 导线残值计算使用`calculate_wire_residual`函数
- 套管残值计算使用`calculate_sleeve_residual`函数
- 残值阈值从`1e-10`提高到`1e-15`

#### sleeve_matching.py
- 套管匹配中的残值计算使用decimal模块
- 提高计算精度和准确性
- 套管残值只在此处计算一次，避免重复

#### 重复计算问题修复
- **问题**: 原来套管残值在`sleeve_matching.py`和`bom_processor.py`中都会计算，导致重复记录
- **解决**: BOM处理中仍然计算套管分子（确保BOM正常输出），但不重复记录残值到残值列表
- **效果**: 确保每个物料的残值只记录一次，同时保证BOM清单正常输出

#### BOM输出兼容性保证
- **保持计算**: BOM处理中继续使用decimal模块计算套管分子，确保数据准确性
- **避免重复**: 不在BOM处理中重复记录套管残值，残值只在`sleeve_matching.py`中记录
- **输出正常**: 残值量表和BOM清单都能正常生成和输出

#### 物料编码格式优化
- **数字转换**: 残值量表中的物料编码自动转换为数字格式
- **智能处理**: 支持字符串数字、浮点数等多种格式的转换
- **兼容性**: 非数字编码保持原始格式，确保特殊编码不受影响

## 技术特点

### 1. 精度控制
```python
# 设置7位有效数字
getcontext().prec = 7

# 使用局部上下文进行高精度中间计算
with localcontext() as ctx:
    ctx.prec = 28  # 中间计算使用28位精度
    ctx.rounding = ROUND_DOWN  # 向下截断
```

### 2. 类型转换
```python
def to_decimal(value):
    """安全地将值转换为Decimal类型"""
    if value is None or pd.isna(value):
        return None
    return Decimal(str(value))

def to_float(decimal_value):
    """将Decimal转换为float以保持pandas兼容性"""
    return float(decimal_value)
```

### 3. 精确截断
```python
# 精确截断到指定小数位数
factor = Decimal('10') ** decimals
truncated = (decimal_value * factor).to_integral_value(rounding=ROUND_DOWN) / factor
```

## 使用示例

### 导线残值计算
```python
from decimal_calculator import calculate_wire_residual

# 计算导线残值
wire_length = 100.123456
loss_rate = 0.05
decimals = 2

truncated_value, residual = calculate_wire_residual(wire_length, loss_rate, decimals)
print(f"截断值: {truncated_value}, 残值: {residual}")
# 输出: 截断值: 105.12, 残值: 0.0096288
```

### 套管残值计算
```python
from decimal_calculator import calculate_sleeve_residual

# 计算套管残值
total_length = 50.789123
unit_length = 1.5
loss_rate = 0.05
decimals = 2

truncated_value, residual = calculate_sleeve_residual(total_length, unit_length, loss_rate, decimals)
print(f"截断值: {truncated_value}, 残值: {residual}")
# 输出: 截断值: 35.55, 残值: 0.0023861
```

### 精确截断函数
```python
from bom_processor import truncate_to_decimals

# 使用改进后的截断函数
result = truncate_to_decimals(123.456789, 3)
print(f"结果: {result}")
# 输出: 结果: 123.456
```

## 测试验证

运行测试脚本验证功能：
```bash
python decimal_calculator.py  # 基础功能测试
python test_decimal_residual.py  # 完整测试套件
python test_residual_duplication.py  # 重复计算问题测试
python test_bom_output_fix.py  # BOM输出修复验证
python test_material_code_conversion.py  # 物料编码转换测试
```

## 兼容性

- 保持与现有代码的完全兼容性
- 函数接口不变，只是内部实现更精确
- 与pandas DataFrame完全兼容
- 支持None值和NaN值的处理

## 性能考虑

- decimal计算比float稍慢，但精度更高
- 对于残值计算这种对精度要求高的场景，性能损失可接受
- 使用局部上下文避免全局性能影响

## 总结

通过使用decimal模块，我们实现了：
1. **更高精度**: 7位有效数字，避免浮点数精度问题
2. **精确截断**: 使用ROUND_DOWN模式确保一致性
3. **准确残值**: 残值计算更加精确可靠
4. **避免重复**: 修复了套管残值重复计算的问题
5. **格式优化**: 残值量表中物料编码自动转换为数字格式
6. **输出保证**: BOM清单和残值量表都能正常输出
7. **向后兼容**: 不影响现有功能和接口
8. **易于维护**: 代码结构清晰，便于后续维护

这次升级显著提高了导线和套管残值计算的准确性，消除了重复计算问题，优化了数据格式，为整个系统提供了更可靠的数据基础。
