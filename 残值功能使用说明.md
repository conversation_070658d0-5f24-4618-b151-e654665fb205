# 残值功能使用说明

## 功能概述

新的残值功能允许用户指定一个残值文件，系统会：
1. 读取用户指定的残值文件中的现有残值
2. 将处理过程中产生的新残值累加到现有残值中
3. 当残值达到物料的最小有效值时，自动应用到BOM清单的分子数量中
4. 更新残值文件，保存剩余的残值

## 主要改进

### ✅ 不再生成汇总表
- **旧版本**: 系统会生成一个新的`残值量.xlsx`汇总表
- **新版本**: 直接在用户指定的残值文件中进行操作，不生成额外的汇总表

### ✅ 支持物料编码匹配和累加
- 如果残值表中已有相同的物料编码，则将新残值与现有残值相加
- 如果残值表中没有对应的物料编码，则添加新的物料行

### ✅ 自动应用残值到BOM清单
- 当物料的残值达到或超过该物料的最小有效值时
- 系统会自动将最小有效值的整数倍加到下一份BOM清单的对应物料分子数量中
- 同时从残值表中减去已应用的数量

## 使用方法

### 1. 在GUI界面中选择残值文件
在所有操作模式（手动选择、自动识别、批量处理）中，都可以选择"残值"文件：
- 残值文件是**可选的**，如果不选择则不会进行残值处理
- 残值文件应该是Excel格式（.xlsx），包含以下列：
  - `物料编码`: 物料的编码
  - `物料名称`: 物料的名称  
  - `残值`: 当前的残值数量

### 2. 残值文件格式示例
```
物料编码    物料名称      残值
10001      1.5平方导线   0.009
10002      套管A型       0.006
```

### 3. 处理流程
1. **读取现有残值**: 系统读取用户指定的残值文件
2. **处理新残值**: 处理过程中产生的新残值会累加到现有残值中
3. **检查最小有效值**: 系统自动检查哪些物料的残值达到了最小有效值
4. **应用到BOM**: 在生成BOM清单时，自动将达到最小有效值的残值加到分子数量中
5. **更新残值文件**: 保存更新后的残值到用户指定的文件中

## 示例场景

假设残值文件中有一物料：
- 物料编码: 10001
- 物料名称: 1.5平方导线
- 当前残值: 0.009
- 最小有效值: 0.01

处理过程中又产生了0.003的残值：
1. **累加残值**: 0.009 + 0.003 = 0.012
2. **检查最小有效值**: 0.012 >= 0.01，可以应用
3. **应用到BOM**: 下一份BOM清单中该物料的分子数量 +0.01
4. **更新残值**: 残值表中该物料的残值变为 0.012 - 0.01 = 0.002

## 技术特性

### 精确计算
- 使用Python的`decimal`模块进行精确的小数计算
- 避免浮点数计算误差

### 自动获取最小有效值
- 从线材规格定义表（Sheet3）中获取导线物料的最小有效值
- 从套管匹配表中获取套管物料的最小有效值

### 动态扩展
- 残值表会自动扩展，包含所有处理过的物料
- 新物料会自动添加到残值表中

### 完整日志
- 所有残值处理操作都有详细的日志记录
- 便于跟踪和调试

## 注意事项

1. **残值文件是可选的**: 如果不指定残值文件，系统仍然正常工作，只是不会进行残值处理
2. **文件格式**: 残值文件必须是Excel格式，包含必要的列
3. **最小有效值**: 系统会自动从配置文件中获取物料的最小有效值，如果找不到则默认为0
4. **备份建议**: 建议在使用前备份原始的残值文件

## 兼容性

- 与现有的所有功能完全兼容
- 不影响不使用残值功能的用户
- 批量处理模式也支持残值功能
