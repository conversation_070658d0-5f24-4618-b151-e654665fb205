#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理器模块
主要功能：自动扫描和处理多个项目
"""

import os
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import glob
import traceback
from datetime import datetime
import logging
import json

# 导入现有的处理模块
from main import handle_selection
from design_doc_processor import process_design_document_auto

# 导入日志配置
from logger_config import get_main_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info, log_file_operation

# 在文件头部导入所需函数
from bom_processor import get_auxiliary_materials_from_cabinet_bom, write_auxiliary_materials_to_bom
import openpyxl


class BatchProcessor:
    """批量处理器类"""
    
    def __init__(self, base_input_dir: str, base_output_dir: str, config_dir: str):
        """
        初始化批量处理器
        
        Args:
            base_input_dir: 基础输入目录（通常是input/Eplan识别目录）
            base_output_dir: 基础输出目录
            config_dir: 配置文件目录
        """
        self.base_input_dir = Path(base_input_dir)
        self.base_output_dir = Path(base_output_dir)
        self.config_dir = Path(config_dir)

        # 日志记录器 - 先初始化logger
        self.logger = get_main_logger()
        self.logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志

        # 配置文件路径 - 查找excel_processor_config.json
        self.config_file_path = self._find_config_file()

        # 处理结果
        self.processed_projects = []
        self.failed_projects = []
        self.skipped_projects = []

        # 进度回调函数
        self.progress_callback = None

        # 配置文件路径
        self.config_files = {}
        self._load_config_files()

    def _find_config_file(self):
        """查找excel_processor_config.json配置文件"""
        # 可能的配置文件位置
        possible_paths = [
            Path(__file__).parent / "excel_processor_config.json",  # 同目录下
            Path(__file__).parent.parent / "excel_processor_config.json",  # 上级目录
            Path(__file__).parent.parent / "src" / "excel_processor_config.json",  # src目录下
        ]

        for config_path in possible_paths:
            if config_path.exists():
                log_file_operation(self.logger, "配置文件", f"找到配置文件: {config_path}")
                return config_path

        log_file_operation(self.logger, "配置文件", "未找到excel_processor_config.json配置文件")
        return None

    def _load_config_files(self):
        """加载配置文件路径"""
        try:
            # 首先尝试从excel_processor_config.json加载配置
            json_config = self._load_json_config()

            # 定义需要的配置文件类型
            config_files = {
                "线材规格定义": "线材规格定义.xlsx",
                "线长定义": "线长定义.xlsx",
                "压头匹配": "压头匹配.xlsx",
                "套管匹配": "套管匹配.xlsx",
                "BOM模板": "BOM清单（模版V1.0）.xlsx",
                "自备料库": "10.全自备料分析.xlsx",
                "物料辅料与把手": "屏柜配料物料清单资料维护2025.7.xlsx",
                "残值": "残值量.xlsx"
            }

            # 优先使用JSON配置中的路径
            if json_config and 'file_paths' in json_config:
                for file_type, file_path in json_config['file_paths'].items():
                    if file_type in config_files and file_path and os.path.exists(file_path):
                        self.config_files[file_type] = file_path
                        log_file_operation(self.logger, "配置文件(JSON)", f"找到{file_type}: {file_path}")

            # 对于未在JSON中找到的文件，使用config_dir目录查找
            for file_type, filename in config_files.items():
                if file_type not in self.config_files:
                    config_path = self.config_dir / filename
                    if config_path.exists():
                        self.config_files[file_type] = str(config_path)
                        log_file_operation(self.logger, "配置文件(目录)", f"找到{file_type}: {config_path}")
                    else:
                        log_file_operation(self.logger, "配置文件", f"未找到{file_type}: {config_path}")

        except Exception as e:
            log_function_error(self.logger, "_load_config_files", str(e))

    def _load_json_config(self):
        """从excel_processor_config.json加载配置"""
        try:
            if self.config_file_path and self.config_file_path.exists():
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    log_file_operation(self.logger, "配置文件", f"成功加载JSON配置: {self.config_file_path}")
                    return config
        except Exception as e:
            log_function_error(self.logger, "_load_json_config", str(e))
        return None
    
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _notify_progress(self, message: str, current: int = 0, total: int = 0):
        """通知进度更新"""
        if self.progress_callback:
            self.progress_callback(message, current, total)
        log_process_step(self.logger, "进度更新", f"{message} ({current}/{total})")
    
    def scan_projects(self) -> List[Dict]:
        """
        扫描项目目录，识别可处理的项目
        智能识别以"SZ"开头的项目号，并正确分离不同项目的文件
        
        Returns:
            List[Dict]: 项目信息列表
        """
        log_function_start(self.logger, "scan_projects", input_dir=str(self.base_input_dir))
        
        projects = []
        
        if not self.base_input_dir.exists():
            log_function_error(self.logger, "scan_projects", f"输入目录不存在: {self.base_input_dir}")
            return projects
        
        # 深度搜索所有文件，按项目号分组
        project_files = self._deep_scan_project_files(self.base_input_dir)
        
        # 为每个项目号创建项目信息
        for project_code, files_info in project_files.items():
            project_info = self._create_project_info(project_code, files_info)
            if project_info:
                projects.append(project_info)
                log_process_step(self.logger, "发现项目", f"{project_code} - {project_info['project_name']}")
        
        log_function_end(self.logger, "scan_projects", f"共发现{len(projects)}个项目")
        return projects
    
    def _deep_scan_project_files(self, scan_dir: Path) -> Dict[str, Dict]:
        """
        深度扫描目录，按项目号分组文件
        
        Args:
            scan_dir: 扫描目录
            
        Returns:
            Dict[str, Dict]: 按项目号分组的文件信息
                格式: {项目号: {文件类型: 文件路径, ...}, ...}
        """
        project_files = {}
        
        try:
            # 递归搜索所有文件
            for file_path in scan_dir.rglob("*"):
                if not file_path.is_file():
                    continue
                
                # 提取项目号
                project_code = self._extract_project_code(file_path)
                if not project_code:
                    continue
                
                # 初始化项目文件字典
                if project_code not in project_files:
                    project_files[project_code] = {
                        'base_path': None,
                        'files': {},
                        'all_paths': []
                    }
                
                # 记录所有相关路径
                project_files[project_code]['all_paths'].append(file_path)
                
                # 识别文件类型
                file_type = self._identify_file_type(file_path)
                if file_type:
                    project_files[project_code]['files'][file_type] = str(file_path)
                    log_file_operation(self.logger, f"{project_code}-{file_type}", f"找到: {file_path}")
                
                # 设置基础路径（使用最近的包含项目号的目录）
                if not project_files[project_code]['base_path']:
                    project_files[project_code]['base_path'] = self._find_project_base_path(file_path, project_code)
                        
        except Exception as e:
            log_function_error(self.logger, "_deep_scan_project_files", f"扫描文件失败: {str(e)}")
        
        return project_files
    
    def _extract_project_code(self, file_path: Path) -> Optional[str]:
        """
        从文件路径中提取项目号（以SZ开头）
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 项目号，如 "SZ2505921"
        """
        import re
        
        # 在文件名和路径中查找SZ开头的项目号
        path_str = str(file_path)
        
        # 匹配SZ开头，后跟数字的模式
        pattern = r'SZ\d+'
        matches = re.findall(pattern, path_str)
        
        if matches:
            # 返回最长的匹配（通常是完整的项目号）
            return max(matches, key=len)
        
        return None
    
    def _identify_file_type(self, file_path: Path) -> Optional[str]:
        """
        识别文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件类型
        """
        file_name = file_path.name.lower()
        # 文件扩展名判断
        is_excel = file_name.endswith('.xls') or file_name.endswith('.xlsx') or file_name.endswith('.xlsm')

        # 优先判断“设计说明书”
        if "设计说明书" in file_name and is_excel:
            return "设计说明书"

        # 文件名包含“套管”且为表格文件
        if "套管" in file_name and is_excel:
            return "屏柜配线套管"

        # 文件名包含“bom”且为表格文件
        if "bom" in file_name and is_excel:
            return "屏柜BOM表"

        return None
    
    def _find_project_base_path(self, file_path: Path, project_code: str) -> str:
        """
        查找项目的基础路径
        
        Args:
            file_path: 文件路径
            project_code: 项目号
            
        Returns:
            str: 项目基础路径
        """
        # 向上查找包含项目号的目录
        for parent in file_path.parents:
            if project_code in parent.name:
                return str(parent.parent)  # 返回项目目录的父目录
        
        return str(file_path.parent)
    
    def _create_project_info(self, project_code: str, files_info: Dict) -> Optional[Dict]:
        """
        创建项目信息
        
        Args:
            project_code: 项目号
            files_info: 文件信息
            
        Returns:
            Dict: 项目信息字典
        """
        try:
            project_files = files_info['files']
            base_path = files_info.get('base_path', '')
            
            # 从设计说明书文件名中提取项目名称
            project_name = project_code
            design_doc_path = project_files.get('设计说明书', '')
            if design_doc_path:
                design_doc_name = Path(design_doc_path).name
                # 尝试从文件名中提取项目名称
                import re
                # 匹配形如 [项目名称] 的模式
                name_match = re.search(r'\[([^\]]+)\]', design_doc_name)
                if name_match:
                    extracted_name = name_match.group(1)
                    # 如果提取的名称不是项目号，则作为项目名称
                    if not extracted_name.startswith('SZ'):
                        project_name = f"{project_code}_{extracted_name}"
            
            project_info = {
                'project_name': project_name,
                'project_code': project_code,
                'project_path': base_path,
                'files': project_files,
                'is_valid': False,
                'error_messages': []
            }
            
            # 检查必要文件是否存在
            required_files = ["屏柜配线套管", "屏柜BOM表", "设计说明书"]
            missing_files = []
            
            for file_type in required_files:
                if file_type not in project_files:
                    missing_files.append(file_type)
            
            if missing_files:
                project_info['error_messages'].append(f"缺少文件: {', '.join(missing_files)}")
            else:
                project_info['is_valid'] = True
            
            return project_info
            
        except Exception as e:
            log_function_error(self.logger, "_create_project_info", f"创建项目信息失败: {str(e)}")
            return None

    def _analyze_project_directory(self, project_dir: Path) -> Optional[Dict]:
        """
        分析单个项目目录，提取项目信息
        
        Args:
            project_dir: 项目目录路径
            
        Returns:
            Dict: 项目信息字典，如果项目无效则返回None
        """
        try:
            project_info = {
                'project_name': project_dir.name,
                'project_path': str(project_dir),
                'files': {},
                'is_valid': False,
                'error_messages': []
            }
            
            # 查找设计说明书
            design_doc_files = list(project_dir.glob("设计说明书*.xls"))
            if design_doc_files:
                project_info['files']['设计说明书'] = str(design_doc_files[0])
                log_file_operation(self.logger, "设计说明书", f"找到: {design_doc_files[0]}")
            else:
                project_info['error_messages'].append("未找到设计说明书")
            
            # 查找项目子目录
            sub_dirs = [d for d in project_dir.iterdir() if d.is_dir()]
            if not sub_dirs:
                project_info['error_messages'].append("未找到项目子目录")
                return project_info
            
            # 通常项目子目录只有一个，选择第一个
            sub_dir = sub_dirs[0]
            
            # 查找图纸/报表目录
            reports_dir = None
            possible_paths = [
                sub_dir / "图纸" / "报表",
                sub_dir / "报表",
                sub_dir / "图纸"
            ]
            
            for path in possible_paths:
                if path.exists():
                    # 查找最新的日期目录
                    date_dirs = [d for d in path.iterdir() if d.is_dir() and d.name.isdigit()]
                    if date_dirs:
                        # 选择最新的日期目录
                        latest_date_dir = max(date_dirs, key=lambda x: x.name)
                        reports_dir = latest_date_dir
                        break
                    else:
                        # 直接使用该目录
                        reports_dir = path
                        break
            
            if not reports_dir:
                project_info['error_messages'].append("未找到报表目录")
                return project_info
            
            # 查找必要的文件
            required_files = {
                "屏柜配线套管": ["导线连接套管.xlsx", "屏柜配线套管.xlsx"],
                "屏柜BOM表": ["屏柜BOM表.xlsm"]
            }
            
            for file_type, possible_names in required_files.items():
                found = False
                for name in possible_names:
                    file_path = reports_dir / name
                    if file_path.exists():
                        project_info['files'][file_type] = str(file_path)
                        log_file_operation(self.logger, file_type, f"找到: {file_path}")
                        found = True
                        break
                
                if not found:
                    project_info['error_messages'].append(f"未找到{file_type}文件")
            
            # 检查项目是否有效
            if ('设计说明书' in project_info['files'] and 
                '屏柜配线套管' in project_info['files'] and
                '屏柜BOM表' in project_info['files']):
                project_info['is_valid'] = True
            
            return project_info
            
        except Exception as e:
            log_function_error(self.logger, "_analyze_project_directory", f"分析项目{project_dir.name}失败: {str(e)}")
            return None
    
    def process_projects(self, projects: List[Dict], interface_mode: str = "自动识别") -> Dict:
        """
        批量处理项目
        
        Args:
            projects: 项目列表
            interface_mode: 界面模式
            
        Returns:
            Dict: 处理结果统计
        """
        log_function_start(self.logger, "process_projects", project_count=len(projects))
        
        # 重置处理结果
        self.processed_projects = []
        self.failed_projects = []
        self.skipped_projects = []
        
        # 创建批量输出目录
        batch_output_dir = self.base_output_dir / f"批量处理_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        batch_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建综合输出表
        all_results = []
        
        total_projects = len(projects)
        
        for i, project_info in enumerate(projects, 1):
            try:
                self._notify_progress(f"处理项目: {project_info['project_name']}", i, total_projects)
                
                if not project_info['is_valid']:
                    skip_reason = '; '.join(project_info['error_messages'])
                    self.skipped_projects.append({
                        'project_name': project_info['project_name'],
                        'reason': skip_reason
                    })
                    log_process_step(self.logger, "跳过项目", f"{project_info['project_name']}: {skip_reason}")
                    # 通过进度回调通知GUI
                    self._notify_progress(f"跳过项目 {project_info['project_name']}: {skip_reason}")
                    continue
                
                # 处理单个项目
                result = self._process_single_project(project_info, batch_output_dir, interface_mode)
                
                if result['success']:
                    self.processed_projects.append(result)
                    log_process_step(self.logger, "项目处理成功", project_info['project_name'])
                    # 通过进度回调通知GUI
                    self._notify_progress(f"✓ 项目 {project_info['project_name']} 处理成功")
                    
                    # 添加到综合结果
                    if result.get('summary_data'):
                        all_results.append(result['summary_data'])
                        
                else:
                    self.failed_projects.append(result)
                    error_msg = result.get('error', '未知错误')
                    log_process_step(self.logger, "项目处理失败", f"{project_info['project_name']}: {error_msg}")
                    # 通过进度回调通知GUI
                    self._notify_progress(f"✗ 项目 {project_info['project_name']} 处理失败: {error_msg}")
                    
            except Exception as e:
                error_msg = f"处理项目{project_info['project_name']}时发生异常: {str(e)}"
                log_function_error(self.logger, "process_projects", error_msg)
                self.failed_projects.append({
                    'project_name': project_info['project_name'],
                    'error': error_msg,
                    'success': False,
                    'residual_data': []
                })
                # 通过进度回调通知GUI
                self._notify_progress(f"✗ 项目 {project_info['project_name']} 发生异常: {error_msg}")
        
        # 注释掉综合汇总报告的生成（根据用户要求）
        # if all_results:
        #     self._notify_progress("正在生成综合汇总报告...")
        #     self._generate_summary_report(all_results, batch_output_dir)

        # 生成残值量汇总表
        self._notify_progress("正在生成残值量汇总表...")
        self._generate_residual_summary(batch_output_dir)

        # 生成处理报告
        self._notify_progress("正在生成处理报告...")
        self._generate_processing_report(batch_output_dir)
        
        # 返回处理统计
        result_summary = {
            'total': total_projects,
            'processed': len(self.processed_projects),
            'failed': len(self.failed_projects),
            'skipped': len(self.skipped_projects),
            'output_dir': str(batch_output_dir)
        }
        
        # 通过进度回调通知最终结果
        self._notify_progress(f"批量处理完成！成功: {len(self.processed_projects)}, 失败: {len(self.failed_projects)}, 跳过: {len(self.skipped_projects)}")
        
        log_function_end(self.logger, "process_projects", f"处理完成: {result_summary}")
        return result_summary
    
    def _process_single_project(self, project_info: Dict, batch_output_dir: Path, interface_mode: str) -> Dict:
        """
        处理单个项目
        
        Args:
            project_info: 项目信息
            batch_output_dir: 批量输出目录
            interface_mode: 界面模式
            
        Returns:
            Dict: 处理结果
        """
        try:
            project_name = project_info['project_name']
            log_process_step(self.logger, "开始处理项目", project_name)
            self._notify_progress(f"开始处理项目: {project_name}")
            
            # 创建项目输出目录
            project_output_dir = batch_output_dir / project_name
            project_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 准备输入文件
            input_files = {}
            
            # 项目特定文件
            input_files.update(project_info['files'])
            
            # 通用配置文件
            input_files.update(self.config_files)
            
            # 处理结果容器
            results = {}
            
            # 第一步：尝试生成BOM清单（如果条件满足）
            log_process_step(self.logger, "尝试生成BOM清单", project_name)
            self._notify_progress(f"正在为 {project_name} 生成BOM清单...")
            try:
                # 首先尝试生成BOM清单（generate_output_only=False）
                residual_data = handle_selection(
                    input_files=input_files,
                    output_path_or_dir=str(project_output_dir),  # 传递输出目录
                    material_type="自动识别",
                    cable_type="自动识别",
                    project_type="通用",
                    interface_mode=interface_mode,
                    generate_output_only=False,  # 允许生成BOM清单
                    app=None,  # 批量处理模式不需要GUI对象
                    save_residual_table=False  # 不保存残值表，返回数据
                )
                # 保存残值数据
                results['residual_data'] = residual_data if residual_data else []
                # 检查是否生成了BOM文件
                bom_files = list(project_output_dir.glob("*.xlsx"))
                bom_files = [f for f in bom_files if "BOM" in f.name or "清单" in f.name or 
                           (f.name.startswith(project_name + "-") and "P" in f.name and f.name != f"{project_name}_综合输出表.xlsx")]

                if bom_files:
                    log_process_step(self.logger, "BOM清单生成成功", f"生成了{len(bom_files)}个BOM文件")
                    self._notify_progress(f"✓ {project_name} BOM清单生成成功，共{len(bom_files)}个文件")
                    results['bom_files'] = [str(f) for f in bom_files]
                else:
                    log_process_step(self.logger, "BOM清单生成", "未生成BOM文件（可能不满足条件）")
                    self._notify_progress(f"⚠ {project_name} 未生成BOM文件（可能不满足条件）")
                    results['bom_files'] = []
                
            except Exception as e:
                error_msg = f"生成BOM清单失败: {str(e)}"
                log_function_error(self.logger, "BOM清单生成", error_msg)
                self._notify_progress(f"✗ {project_name} BOM清单生成失败: {error_msg}")
                results['bom_error'] = str(e)
                results['bom_files'] = []
                results['residual_data'] = []  # 失败时也初始化残值数据
            
            # 第二步：生成综合输出表
            log_process_step(self.logger, "生成综合输出表", project_name)
            self._notify_progress(f"正在为 {project_name} 生成综合输出表...")
            output_path = project_output_dir / f"{project_name}_综合输出表.xlsx"
            
            try:
                # 调用处理函数生成输出表
                handle_selection(
                    input_files=input_files,
                    output_path_or_dir=str(output_path),
                    material_type="自动识别",
                    cable_type="自动识别",
                    project_type="通用",
                    interface_mode=interface_mode,
                    generate_output_only=True,  # 只生成输出表
                    app=None,  # 批量处理模式不需要GUI对象
                    save_residual_table=False  # 不保存残值表
                )
                
                # 检查输出文件是否生成
                if output_path.exists():
                    log_process_step(self.logger, "输出表生成成功", str(output_path))
                    self._notify_progress(f"✓ {project_name} 综合输出表生成成功")
                    results['output_file'] = str(output_path)
                    
                    # 读取处理结果用于汇总
                    summary_data = self._extract_summary_data(output_path, project_name)
                    results['summary_data'] = summary_data
                    
                else:
                    error_msg = "输出文件未生成"
                    log_function_error(self.logger, "输出表生成", error_msg)
                    self._notify_progress(f"✗ {project_name} 输出表生成失败: {error_msg}")
                    results['output_error'] = error_msg
                    
            except Exception as e:
                error_msg = f"生成输出表失败: {str(e)}"
                log_function_error(self.logger, "输出表生成", error_msg)
                self._notify_progress(f"✗ {project_name} 输出表生成失败: {error_msg}")
                results['output_error'] = str(e)
            
            # 准备返回结果
            success = 'output_file' in results  # 至少要有输出表才算成功
            
            if success:
                result = {
                    'project_name': project_name,
                    'output_file': results.get('output_file'),
                    'bom_files': results.get('bom_files', []),
                    'success': True,
                    'summary_data': results.get('summary_data', {}),
                    'has_bom': len(results.get('bom_files', [])) > 0,
                    'residual_data': results.get('residual_data', [])  # 添加残值数据
                }

                if results.get('bom_error'):
                    result['bom_error'] = results['bom_error']

            else:
                result = {
                    'project_name': project_name,
                    'success': False,
                    'error': results.get('output_error', '未知错误'),
                    'bom_files': results.get('bom_files', []),
                    'has_bom': len(results.get('bom_files', [])) > 0,
                    'residual_data': results.get('residual_data', [])  # 添加残值数据
                }

                if results.get('bom_error'):
                    result['bom_error'] = results['bom_error']
            
            return result
                
        except Exception as e:
            error_msg = f"处理项目{project_info['project_name']}失败: {str(e)}"
            log_function_error(self.logger, "_process_single_project", error_msg)
            self._notify_progress(f"✗ {project_info['project_name']} 处理失败: {error_msg}")
            return {
                'project_name': project_info['project_name'],
                'error': error_msg,
                'success': False,
                'bom_files': [],
                'has_bom': False,
                'residual_data': []
            }
    
    def _extract_summary_data(self, output_file: Path, project_name: str) -> Dict:
        """
        从输出文件中提取汇总数据
        
        Args:
            output_file: 输出文件路径
            project_name: 项目名称
            
        Returns:
            Dict: 汇总数据
        """
        try:
            # 读取各个工作表的数据
            # 使用ExcelFile来避免类型检查问题
            excel_file = pd.ExcelFile(output_file, engine='openpyxl')
            excel_data = {}
            for sheet_name in excel_file.sheet_names:
                excel_data[sheet_name] = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            summary = {
                '项目名称': project_name,
                '导线统计数量': len(excel_data.get('导线统计', pd.DataFrame())),
                '并线统计数量': len(excel_data.get('并线统计', pd.DataFrame())),
                '数据线记录数量': len(excel_data.get('数据线记录', pd.DataFrame())),
                '电源线记录数量': len(excel_data.get('电源线记录', pd.DataFrame())),
                '压头匹配数量': len(excel_data.get('压头匹配', pd.DataFrame())),
                '套管匹配数量': len(excel_data.get('套管匹配', pd.DataFrame())),
                '色带用量数量': len(excel_data.get('色带用量', pd.DataFrame())),
                '输出文件': str(output_file)
            }
            
            return summary
            
        except Exception as e:
            log_function_error(self.logger, "_extract_summary_data", f"提取汇总数据失败: {str(e)}")
            return {
                '项目名称': project_name,
                '错误': str(e),
                '输出文件': str(output_file)
            }
    
    def _generate_summary_report(self, all_results: List[Dict], output_dir: Path):
        """
        生成综合汇总报告
        
        Args:
            all_results: 所有项目的汇总数据
            output_dir: 输出目录
        """
        try:
            summary_file = output_dir / "所有项目-综合汇总报告.xlsx"
            
            # 定义标准字段结构
            standard_fields = {
                '项目名称': '',
                '是否生成BOM': '否',
                'BOM清单文件数': 0,
                'BOM文件列表': '',
                'BOM生成错误': '',
                '导线统计数量': 0,
                '并线统计数量': 0,
                '数据线记录数量': 0,
                '电源线记录数量': 0,
                '压头匹配数量': 0,
                '套管匹配数量': 0,
                '色带用量数量': 0,
                '输出文件': '',
                '错误': ''
            }
            
            # 准备汇总数据
            summary_data = []
            for result in all_results:
                # 创建标准行，先填入默认值
                summary_row = standard_fields.copy()
                
                # 填入基础数据
                if result.get('summary_data'):
                    for key, value in result['summary_data'].items():
                        if key in summary_row:
                            summary_row[key] = value
                
                # 添加BOM清单信息
                summary_row['BOM清单文件数'] = len(result.get('bom_files', []))
                summary_row['是否生成BOM'] = '是' if result.get('has_bom', False) else '否'
                
                # 添加BOM文件列表
                bom_files = result.get('bom_files', [])
                if bom_files:
                    # 只保留文件名，不包括完整路径
                    bom_file_names = [os.path.basename(f) for f in bom_files]
                    summary_row['BOM文件列表'] = '; '.join(bom_file_names)
                
                # 添加BOM错误信息
                if result.get('bom_error'):
                    summary_row['BOM生成错误'] = result['bom_error']
                
                # 添加处理错误信息
                if result.get('error'):
                    summary_row['错误'] = result['error']
                
                summary_data.append(summary_row)
            
            # 转换为DataFrame
            if summary_data:
                df = pd.DataFrame(summary_data)
                
                # 调整列顺序，将重要信息放在前面
                columns_order = [
                    '项目名称', 
                    '是否生成BOM', 
                    'BOM清单文件数', 
                    'BOM文件列表', 
                    'BOM生成错误',
                    '导线统计数量',
                    '并线统计数量', 
                    '数据线记录数量',
                    '电源线记录数量',
                    '压头匹配数量', 
                    '套管匹配数量',
                    '色带用量数量',
                    '输出文件',
                    '错误'
                ]
                
                # 确保列顺序正确
                final_columns = [col for col in columns_order if col in df.columns]
                df = df[final_columns]
                
                # 保存到Excel
                df.to_excel(summary_file, index=False, engine='openpyxl')
                
                log_file_operation(self.logger, "综合汇总报告", f"已生成: {summary_file}")
            else:
                # 如果没有数据，创建一个空的DataFrame
                df = pd.DataFrame({'项目名称': [], '错误': ['没有有效的汇总数据']})
                df.to_excel(summary_file, index=False, engine='openpyxl')
                log_function_error(self.logger, "_generate_summary_report", "没有有效的汇总数据")
            
        except Exception as e:
            log_function_error(self.logger, "_generate_summary_report", f"生成综合汇总报告失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _generate_residual_summary(self, output_dir: Path):
        """
        生成残值量汇总表

        Args:
            output_dir: 输出目录
        """
        try:
            # 收集所有项目的残值数据
            all_residuals = []

            # 检查成功处理的项目
            log_process_step(self.logger, "收集残值数据", f"成功项目数: {len(self.processed_projects)}")
            for project in self.processed_projects:
                residual_data = project.get('residual_data', [])
                project_name = project.get('project_name', '未知项目')
                log_process_step(self.logger, f"项目{project_name}残值数据", f"数量: {len(residual_data)}")
                if residual_data:
                    # 为每个残值记录添加项目名称
                    for residual in residual_data:
                        if isinstance(residual, dict):
                            residual_with_project = residual.copy()
                            residual_with_project['项目名称'] = project_name
                            all_residuals.append(residual_with_project)

            # 也检查失败项目中可能有的残值数据
            log_process_step(self.logger, "收集残值数据", f"失败项目数: {len(self.failed_projects)}")
            for project in self.failed_projects:
                residual_data = project.get('residual_data', [])
                project_name = project.get('project_name', '未知项目')
                log_process_step(self.logger, f"失败项目{project_name}残值数据", f"数量: {len(residual_data)}")
                if residual_data:
                    # 为每个残值记录添加项目名称
                    for residual in residual_data:
                        if isinstance(residual, dict):
                            residual_with_project = residual.copy()
                            residual_with_project['项目名称'] = project_name
                            all_residuals.append(residual_with_project)

            log_process_step(self.logger, "收集到的残值数据总数", f"{len(all_residuals)}条")

            if all_residuals:
                # 导入merge_residual_lists函数
                from main import merge_residual_lists

                # 按物料编码合并残值
                merged_residuals = merge_residual_lists(all_residuals)

                # 创建DataFrame
                df = pd.DataFrame(merged_residuals)

                # 保存到Excel
                residual_file = output_dir / "残值量汇总表.xlsx"
                df.to_excel(residual_file, index=False, engine='openpyxl')

                log_file_operation(self.logger, "残值量汇总表", f"已生成: {residual_file}")
                log_data_info(self.logger, "残值量汇总", f"共{len(merged_residuals)}种物料")
            else:
                log_process_step(self.logger, "残值量汇总", "没有残值数据，不生成汇总表")

        except Exception as e:
            log_function_error(self.logger, "_generate_residual_summary", f"生成残值量汇总表失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _generate_processing_report(self, output_dir: Path):
        """
        生成处理报告
        
        Args:
            output_dir: 输出目录
        """
        try:
            report_file = output_dir / "批量处理报告.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("批量处理报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write(f"处理统计:\n")
                f.write(f"  总项目数: {len(self.processed_projects) + len(self.failed_projects) + len(self.skipped_projects)}\n")
                f.write(f"  成功处理: {len(self.processed_projects)}\n")
                f.write(f"  处理失败: {len(self.failed_projects)}\n")
                f.write(f"  跳过项目: {len(self.skipped_projects)}\n\n")
                
                # 统计BOM清单生成情况
                bom_success_count = 0
                bom_total_files = 0
                for project in self.processed_projects:
                    if project.get('has_bom', False):
                        bom_success_count += 1
                        bom_total_files += len(project.get('bom_files', []))
                
                f.write(f"BOM清单生成统计:\n")
                f.write(f"  成功生成BOM的项目数: {bom_success_count}\n")
                f.write(f"  BOM清单文件总数: {bom_total_files}\n\n")
                
                if self.processed_projects:
                    f.write("成功处理的项目:\n")
                    for project in self.processed_projects:
                        f.write(f"  - {project['project_name']}")
                        if project.get('has_bom', False):
                            f.write(f" (已生成{len(project.get('bom_files', []))}个BOM文件)")
                        f.write("\n")
                        
                        # 如果有BOM文件，列出文件名
                        if project.get('bom_files'):
                            for bom_file in project['bom_files']:
                                f.write(f"    BOM文件: {os.path.basename(bom_file)}\n")
                        
                        # 如果BOM生成有错误，记录错误信息
                        if project.get('bom_error'):
                            f.write(f"    BOM生成错误: {project['bom_error']}\n")
                    f.write("\n")
                
                if self.failed_projects:
                    f.write("处理失败的项目:\n")
                    for project in self.failed_projects:
                        f.write(f"  - {project['project_name']}: {project['error']}\n")
                        
                        # 如果有BOM相关错误，也记录下来
                        if project.get('bom_error'):
                            f.write(f"    BOM生成错误: {project['bom_error']}\n")
                    f.write("\n")
                
                if self.skipped_projects:
                    f.write("跳过的项目:\n")
                    for project in self.skipped_projects:
                        f.write(f"  - {project['project_name']}: {project['reason']}\n")
                    f.write("\n")
            
            log_file_operation(self.logger, "处理报告", f"已生成: {report_file}")
            
        except Exception as e:
            log_function_error(self.logger, "_generate_processing_report", f"生成处理报告失败: {str(e)}")


def run_batch_processing(input_dir: str, output_dir: str, config_dir: str, 
                        progress_callback=None) -> Dict:
    """
    运行批量处理
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        config_dir: 配置目录
        progress_callback: 进度回调函数
        
    Returns:
        Dict: 处理结果统计
    """
    processor = BatchProcessor(input_dir, output_dir, config_dir)
    
    if progress_callback:
        processor.set_progress_callback(progress_callback)
    
    # 扫描项目
    projects = processor.scan_projects()
    
    if not projects:
        return {
            'total': 0,
            'processed': 0,
            'failed': 0,
            'skipped': 0,
            'output_dir': output_dir,
            'error': '未找到可处理的项目'
        }
    
    # 处理项目
    result = processor.process_projects(projects)
    
    return result 