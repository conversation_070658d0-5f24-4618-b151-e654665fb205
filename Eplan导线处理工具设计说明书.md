# Eplan导线处理工具设计说明书

## 1. 项目概述

### 1.1 项目背景
Eplan导线处理工具是一个专业的电气设计辅助软件，主要用于处理电气设计中的屏柜配线数据，自动生成导线统计、BOM清单等各种报表。该工具能够显著提高电气设计工程师的工作效率，减少人工计算错误。

### 1.2 项目目标
- 自动化处理屏柜配线套管数据
- 智能识别和统计各类导线信息
- 生成标准化的BOM清单和统计报表
- 支持多种项目类型和特殊需求
- 提供友好的图形用户界面
- 实现多线程优化以提升处理性能

### 1.3 技术架构
- **开发语言**: Python 3.7+
- **GUI框架**: Tkinter
- **数据处理**: Pandas, OpenPyXL
- **多线程**: concurrent.futures
- **日志系统**: logging
- **数值计算**: decimal (高精度计算)

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层    │    │   业务逻辑层    │    │   数据访问层    │
│                 │    │                 │    │                 │
│ - GUI界面       │    │ - 数据处理      │    │ - Excel读写     │
│ - 文件选择      │    │ - 统计计算      │    │ - 配置文件      │
│ - 参数配置      │    │ - 匹配算法      │    │ - 日志记录      │
│ - 进度显示      │    │ - BOM生成       │    │ - 残值管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块划分

#### 2.2.1 核心模块
- **main.py**: 主程序入口，控制整体流程
- **excel_file_selector.py**: GUI界面和文件选择
- **multithreading_processor.py**: 多线程优化模块

#### 2.2.2 数据处理模块
- **preprocess_wiring_table.py**: 数据预处理
- **short_wire_processor.py**: 短接线识别
- **parallel_wire_statistics.py**: 并线统计
- **wire_count_processor.py**: 导线根数统计
- **wire_length_processor.py**: 导线长度计算

#### 2.2.3 匹配模块
- **wire_code_matcher.py**: 导线编码匹配
- **wire_diameter_matcher.py**: 导线线径匹配
- **terminal_matching.py**: 压头匹配
- **sleeve_matching.py**: 套管匹配
- **sleeve_matching_fujian.py**: 福建特殊项目套管匹配

#### 2.2.4 输出模块
- **color_band_processor.py**: 色带用量统计
- **design_doc_processor.py**: 设计说明书处理
- **bom_processor.py**: BOM清单生成
- **residual_manager.py**: 残值管理

#### 2.2.5 工具模块
- **logger_config.py**: 日志配置
- **decimal_calculator.py**: 高精度数值计算
- **excel_formatter.py**: Excel格式化

## 3. 功能设计

### 3.1 主要功能

#### 3.1.1 数据预处理
- **回车符处理**: 清理导线起点和终点中的回车符
- **设备类型识别**: 根据设备标号智能识别设备类型
- **数据清洗**: 标准化颜色/线径标识格式
- **短接线识别**: 自动识别和分离短接线数据

#### 3.1.2 导线统计
- **并线统计**: 查找并统计并接导线
- **根数统计**: 按屏柜编号和设备类型统计导线根数
- **长度计算**: 根据设备类型计算导线长度
- **特殊处理**: 支持旋转柜长度翻倍计算

#### 3.1.3 匹配功能
- **编码匹配**: 根据线材规格自动匹配导线编码
- **线径匹配**: 根据颜色标识匹配对应线径
- **压头匹配**: 根据线径匹配相应的压头规格
- **套管匹配**: 根据导线数量匹配套管规格

#### 3.1.4 BOM生成
- **自动识别**: 从设计说明书自动提取参数
- **模板处理**: 基于BOM模板生成标准清单
- **物料匹配**: 从自备料库匹配物料信息
- **多屏柜支持**: 支持多个屏柜的BOM并行生成

### 3.2 特色功能

#### 3.2.1 多线程优化
- **并行文件读取**: 同时读取多个配置文件
- **并行长度计算**: 同时计算并线的两个长度
- **并行匹配操作**: 同时执行压头和套管匹配
- **并行BOM生成**: 多个屏柜的BOM并行处理

#### 3.2.2 智能识别
- **设计说明书解析**: 自动提取线材规格和线径选型
- **柜体类型识别**: 识别旋转柜等特殊类型
- **小母线数据提取**: 自动提取小母线支架和铜棒信息

#### 3.2.3 残值管理
- **残值跟踪**: 记录处理过程中的物料残值
- **最小有效值**: 应用最小有效值规则
- **残值汇总**: 生成残值统计报告

## 4. 数据流设计

### 4.1 数据流程图
```
输入文件 → 数据预处理 → 短接线识别 → 并线统计 → 导线统计 → 
长度计算 → 编码匹配 → 线径匹配 → 压头匹配 → 套管匹配 → 
色带统计 → BOM生成 → 残值管理 → 输出文件
```

### 4.2 数据结构

#### 4.2.1 主要数据表
- **屏柜配线套管表**: 原始导线数据
- **导线统计表**: 按类型统计的导线数据
- **并线统计表**: 并接导线的统计数据
- **压头匹配表**: 压头规格和数量
- **套管匹配表**: 套管规格和数量
- **BOM清单**: 最终的物料清单

#### 4.2.2 关键字段
- **屏柜编号**: 标识不同的屏柜
- **设备类型**: 起点/终点设备类型
- **颜色/线径标识**: 导线的颜色和线径信息
- **导线根数**: 统计的导线数量
- **单根长度**: 单根导线的长度
- **总长度**: 总的导线长度
- **对应编码**: 匹配的物料编码

## 5. 用户界面设计

### 5.1 界面布局
- **标签页设计**: 手动选择和自动识别两个标签页
- **文件选择区**: 各种输入文件的选择界面
- **参数配置区**: 线材规格、线径选型等参数设置
- **进度显示区**: 处理进度的实时显示
- **状态栏**: 显示当前操作状态和结果

### 5.2 交互设计
- **文件拖拽**: 支持文件拖拽选择
- **配置保存**: 自动保存用户配置
- **错误提示**: 友好的错误信息提示
- **进度反馈**: 实时的处理进度反馈

## 6. 性能优化

### 6.1 多线程优化
- **I/O密集型任务**: 文件读取使用多线程
- **CPU密集型任务**: 数据计算使用多线程
- **线程安全**: 使用线程安全的进度回调
- **资源控制**: 限制最大线程数避免资源耗尽

### 6.2 内存优化
- **分批处理**: 大数据集分批处理
- **及时释放**: 及时释放不需要的数据
- **数据复用**: 避免重复创建相同数据

### 6.3 算法优化
- **索引优化**: 使用索引加速数据查找
- **缓存机制**: 缓存频繁访问的数据
- **并行算法**: 使用并行算法提升计算效率

## 7. 错误处理

### 7.1 异常处理策略
- **分层异常处理**: 不同层级的异常处理
- **异常日志记录**: 详细记录异常信息
- **用户友好提示**: 将技术异常转换为用户可理解的提示
- **异常恢复**: 尽可能从异常中恢复

### 7.2 数据验证
- **输入验证**: 验证输入文件格式和内容
- **数据完整性**: 检查必要字段的完整性
- **数值范围**: 验证数值的合理范围
- **逻辑一致性**: 检查数据的逻辑一致性

## 8. 日志系统

### 8.1 日志分类
- **主程序日志**: main.log
- **数据处理日志**: 各处理模块的专用日志
- **错误日志**: 记录所有错误和异常
- **性能日志**: 记录性能相关信息

### 8.2 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误信息

## 9. 配置管理

### 9.1 配置文件
- **用户配置**: 保存用户的文件路径和参数选择
- **系统配置**: 系统级别的配置参数
- **模板配置**: BOM模板和匹配规则配置

### 9.2 配置策略
- **自动保存**: 用户操作后自动保存配置
- **版本兼容**: 保持配置文件的向后兼容性
- **默认配置**: 提供合理的默认配置

## 10. 扩展性设计

### 10.1 模块化设计
- **松耦合**: 模块间保持松耦合关系
- **接口标准化**: 定义标准的模块接口
- **插件机制**: 支持功能插件的扩展

### 10.2 数据格式扩展
- **多格式支持**: 支持多种Excel格式
- **自定义格式**: 支持用户自定义数据格式
- **格式转换**: 提供格式转换功能

## 11. 测试策略

### 11.1 测试类型
- **单元测试**: 各模块的单元测试
- **集成测试**: 模块间的集成测试
- **性能测试**: 大数据量的性能测试
- **用户测试**: 真实用户场景测试

### 11.2 测试数据
- **标准测试数据**: 标准的测试数据集
- **边界测试数据**: 边界条件的测试数据
- **异常测试数据**: 异常情况的测试数据

## 12. 部署和维护

### 12.1 部署方式
- **便携包部署**: 打包成便携式应用
- **安装包部署**: 提供标准安装包
- **绿色版部署**: 免安装的绿色版本

### 12.2 维护策略
- **版本管理**: 规范的版本管理策略
- **更新机制**: 自动或手动更新机制
- **用户支持**: 提供用户支持和帮助文档

## 13. 安全考虑

### 13.1 数据安全
- **数据备份**: 重要数据的自动备份
- **数据恢复**: 数据损坏时的恢复机制
- **访问控制**: 对敏感数据的访问控制

### 13.2 系统安全
- **输入验证**: 严格的输入数据验证
- **权限控制**: 适当的文件系统权限
- **异常处理**: 安全的异常处理机制

## 14. 未来发展

### 14.1 功能扩展
- **云端处理**: 支持云端数据处理
- **移动端支持**: 开发移动端应用
- **AI集成**: 集成人工智能算法

### 14.2 技术升级
- **框架升级**: 升级到更新的技术框架
- **性能优化**: 持续的性能优化
- **用户体验**: 不断改善用户体验

## 15. 附录

### 15.1 文件结构
```
Eplan-导线/
├── main.py                          # 主程序入口
├── excel_file_selector.py           # GUI界面
├── multithreading_processor.py      # 多线程处理
├── preprocess_wiring_table.py       # 数据预处理
├── short_wire_processor.py          # 短接线处理
├── parallel_wire_statistics.py      # 并线统计
├── wire_count_processor.py          # 导线统计
├── wire_length_processor.py         # 长度计算
├── wire_code_matcher.py             # 编码匹配
├── wire_diameter_matcher.py         # 线径匹配
├── terminal_matching.py             # 压头匹配
├── sleeve_matching.py               # 套管匹配
├── sleeve_matching_fujian.py        # 福建套管匹配
├── color_band_processor.py          # 色带处理
├── design_doc_processor.py          # 设计说明书处理
├── bom_processor.py                 # BOM生成
├── residual_manager.py              # 残值管理
├── logger_config.py                 # 日志配置
├── decimal_calculator.py            # 数值计算
├── excel_formatter.py               # Excel格式化
├── batch_processor.py               # 批量处理
├── input/                           # 输入文件目录
├── output/                          # 输出文件目录
├── logs/                            # 日志文件目录
└── 设计软件导线处理工具_便携包/      # 便携包目录
```

### 15.2 配置文件说明
- **excel_processor_config.json**: 主配置文件
- **input/配置文件/**: 各种匹配规则配置文件
- **logs/**: 各模块的日志文件

### 15.3 输入文件格式
- **屏柜配线套管.xlsx**: 主要的导线数据文件
- **线长定义.xlsx**: 导线长度定义文件
- **线材规格定义.xlsx**: 线材规格和编码定义
- **压头匹配.xlsx**: 压头匹配规则文件
- **套管匹配.xlsx**: 套管匹配规则文件
- **设计说明书.xls**: 设计说明书文件（自动识别模式）
- **BOM模板.xlsx**: BOM清单模板文件
- **自备料库.xlsx**: 物料库文件
- **残值.xlsx**: 残值记录文件（可选）

### 15.4 输出文件格式
- **导线统计表**: 包含8个工作表的Excel文件
  - 短接线表
  - 导线统计表
  - 并线统计表
  - 数据线记录表
  - 电源线记录表
  - 压头匹配表
  - 套管匹配表
  - 色带用量表
- **BOM清单**: 按屏柜生成的BOM文件
- **残值汇总**: 残值统计报告

---

**文档版本**: 1.0
**编写日期**: 2025年7月
**最后更新**: 2025年7月
**编写人员**: 
**审核状态**: 待审核
