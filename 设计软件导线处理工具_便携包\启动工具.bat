@echo off
title Eplan Wire Processing Tool - Portable

cd /d "%~dp0"

echo.
echo ========================================
echo   Eplan Wire Processing Tool - Portable
echo ========================================
echo.

REM Check Python
echo [1/4] Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.10+ from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo OK: Python found
python --version
echo.

REM Check files
echo [2/4] Checking files...
if not exist "src\main.py" (
    echo ERROR: Missing main.py
    pause
    exit /b 1
)

echo OK: Files complete
echo.

REM Setup environment
echo [3/4] Setting up environment...
set PYTHONPATH=%cd%\src;%cd%\data;%PYTHONPATH%

REM Install packages
echo [4/4] Installing packages...

echo Checking pandas...
python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo Installing pandas...
    pip install pandas --quiet
) else (
    echo OK: pandas installed
)

echo Checking openpyxl...
python -c "import openpyxl" >nul 2>&1
if errorlevel 1 (
    echo Installing openpyxl...
    pip install openpyxl --quiet
) else (
    echo OK: openpyxl installed
)

echo Checking numpy...
python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo Installing numpy...
    pip install numpy --quiet
) else (
    echo OK: numpy installed
)

echo Checking xlsxwriter...
python -c "import xlsxwriter" >nul 2>&1
if errorlevel 1 (
    echo Installing xlsxwriter...
    pip install xlsxwriter --quiet
) else (
    echo OK: xlsxwriter installed
)

echo Checking xlrd...
python -c "import xlrd" >nul 2>&1
if errorlevel 1 (
    echo Installing xlrd...
    pip install xlrd --quiet
) else (
    echo OK: xlrd installed
)

echo.
echo ========================================
echo Starting program...
echo ========================================
echo.

cd src
python main.py
cd ..

echo.
echo ========================================
if errorlevel 1 (
    echo Program finished with errors
) else (
    echo Program completed successfully
)
echo ========================================

pause
