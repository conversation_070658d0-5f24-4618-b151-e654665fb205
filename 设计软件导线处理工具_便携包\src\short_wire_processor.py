# short_wire_processor.py

import pandas as pd
import logging
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ShortWireProcessor')
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def identify_short_wires(wiring_df):
    """
    识别短接线
    
    功能：
    1. 搜索"导线连接套管"表的"屏柜编号"，将相同编号的导线行分成一组
    2. 筛选出设备类型（起点/终点）为"装置/装置"的导线
    3. 筛选出颜色/线径标识中带有"U"或带有"I"的导线
    4. 以"导线起点"为准，以"/"为分界线，分成左右两端
    5. 确定规则，例如：1-3n-D:02，1-3n为设备标号，D为板件号，02为接线端号
    6. 遍历筛选出来每一行的"导线起点"，先查看左右两端设备标号是否一致，再查看板件号是否一致，最后看接线端号是否为连续的偶数
    7. 若6中三个条件均满足，则该导线不进行后续的所有操作，独立出来单独记录在数据输出表中新创建的表格"短接线"
    
    参数:
    wiring_df -- 包含屏柜配线套管数据的DataFrame
    
    返回:
    short_wires_df -- 短接线DataFrame
    filtered_wiring_df -- 过滤掉短接线后的原始DataFrame
    """
    try:
        logger.info("开始识别短接线...")
        logger.info(f"输入数据行数: {len(wiring_df)}")
        
        # 检查必要的列是否存在
        required_columns = ['屏柜编号', '设备类型（起点/终点）', '导线起点', '导线终点', '颜色/线径标识']
        missing_columns = [col for col in required_columns if col not in wiring_df.columns]
        if missing_columns:
            logger.error(f"缺少必要的列: {missing_columns}")
            raise ValueError(f"缺少必要的列: {missing_columns}")
        
        # 1. 按屏柜编号分组
        logger.info("按屏柜编号分组...")
        grouped_by_cabinet = wiring_df.groupby('屏柜编号')
        
        short_wires = []
        short_wire_indices = set()
        
        # 2. 遍历每个屏柜
        for cabinet, cabinet_group in grouped_by_cabinet:
            logger.debug(f"处理屏柜: {cabinet}, 导线数量: {len(cabinet_group)}")
            
            # 3. 筛选条件1：设备类型（起点/终点）为"装置/装置"
            device_filter = cabinet_group['设备类型（起点/终点）'] == '装置/装置'
            device_filtered = cabinet_group[device_filter]
            
            if device_filtered.empty:
                logger.debug(f"屏柜 {cabinet} 中没有装置/装置类型的导线")
                continue
                
            logger.debug(f"屏柜 {cabinet} 中装置/装置类型导线数量: {len(device_filtered)}")
            
            # 4. 筛选条件2：颜色/线径标识中带有"U"或带有"I"
            color_filter = (
                device_filtered['颜色/线径标识'].str.contains('U', na=False) |
                device_filtered['颜色/线径标识'].str.contains('I', na=False)
            )
            color_filtered = device_filtered[color_filter]
            
            if color_filtered.empty:
                logger.debug(f"屏柜 {cabinet} 中没有包含U或I的导线")
                continue
                
            logger.debug(f"屏柜 {cabinet} 中包含U或I的导线数量: {len(color_filtered)}")
            
            # 5. 处理每一行导线
            for idx, row in color_filtered.iterrows():
                wire_start = str(row['导线起点'])
                
                # 检查是否包含"/"分隔符
                if '/' not in wire_start:
                    logger.debug(f"导线起点 {wire_start} 不包含分隔符，跳过")
                    continue
                
                # 6. 解析导线起点
                try:
                    left_part, right_part = wire_start.split('/', 1)
                    
                    # 解析左右两端
                    left_info = parse_connection_point(left_part)
                    right_info = parse_connection_point(right_part)
                    
                    if left_info is None or right_info is None:
                        logger.debug(f"无法解析导线起点: {wire_start}")
                        continue
                    
                    # 7. 检查三个条件
                    # 条件1：左右两端设备标号是否一致
                    if left_info['device_code'] != right_info['device_code']:
                        logger.debug(f"设备标号不一致: {left_info['device_code']} vs {right_info['device_code']}")
                        continue
                    
                    # 条件2：板件号是否一致
                    if left_info['board_code'] != right_info['board_code']:
                        logger.debug(f"板件号不一致: {left_info['board_code']} vs {right_info['board_code']}")
                        continue
                    
                    # 条件3：接线端号是否为连续的偶数
                    if not are_consecutive_even_numbers(left_info['terminal_number'], right_info['terminal_number']):
                        logger.debug(f"接线端号不是连续偶数: {left_info['terminal_number']} vs {right_info['terminal_number']}")
                        continue
                    
                    # 所有条件都满足，识别为短接线
                    logger.info(f"识别到短接线: {wire_start}")
                    short_wires.append({
                        '屏柜编号': row['屏柜编号'],
                        '设备类型（起点/终点）': row['设备类型（起点/终点）'],
                        '导线起点': row['导线起点'],
                        '导线终点': row['导线终点'],
                        '颜色/线径标识': row['颜色/线径标识']
                    })
                    short_wire_indices.add(idx)
                    
                except Exception as e:
                    logger.warning(f"解析导线起点时出错: {wire_start}, 错误: {str(e)}")
                    continue
        
        # 创建短接线DataFrame
        short_wires_df = pd.DataFrame(short_wires)
        
        # 过滤掉短接线，返回剩余的导线数据
        filtered_wiring_df = wiring_df[~wiring_df.index.isin(short_wire_indices)].copy()
        
        logger.info(f"短接线识别完成，共识别到 {len(short_wires_df)} 条短接线")
        logger.info(f"过滤后剩余导线数量: {len(filtered_wiring_df)}")
        
        return short_wires_df, filtered_wiring_df
        
    except Exception as e:
        logger.exception("识别短接线时发生错误")
        raise


def parse_connection_point(connection_str):
    """
    解析连接点字符串
    
    规则：例如：1-3n-D:02，1-3n为设备标号，D为板件号，02为接线端号
    
    参数:
    connection_str -- 连接点字符串
    
    返回:
    dict -- 包含解析结果的字典，格式为：
    {
        'device_code': '设备标号',
        'board_code': '板件号', 
        'terminal_number': '接线端号'
    }
    如果解析失败返回None
    """
    try:
        # 去除空白字符
        connection_str = connection_str.strip()
        
        # 检查是否包含冒号分隔符
        if ':' not in connection_str:
            logger.debug(f"连接点字符串不包含冒号分隔符: {connection_str}")
            return None
        
        # 分割设备部分和接线端号部分
        device_part, terminal_part = connection_str.split(':', 1)
        
        # 解析接线端号
        terminal_number = terminal_part.strip()
        
        # 验证接线端号是否为数字
        if not terminal_number.isdigit():
            logger.debug(f"接线端号不是数字: {terminal_number}")
            return None
        
        # 解析设备部分，寻找板件号（通常是单个字母）
        # 设备标号通常包含数字和字母，板件号通常是单个字母
        device_code = device_part.strip()
        
        # 尝试从设备代码中提取板件号
        # 假设板件号是设备代码中的最后一个字母
        board_code = None
        for i in range(len(device_code) - 1, -1, -1):
            if device_code[i].isalpha():
                board_code = device_code[i]
                device_code = device_code[:i]
                break
        
        if board_code is None:
            logger.debug(f"无法从设备代码中提取板件号: {device_part}")
            return None
        
        return {
            'device_code': device_code.strip(),
            'board_code': board_code,
            'terminal_number': terminal_number
        }
        
    except Exception as e:
        logger.warning(f"解析连接点字符串时出错: {connection_str}, 错误: {str(e)}")
        return None


def are_consecutive_even_numbers(num1_str, num2_str):
    """
    检查两个数字是否为连续的偶数
    
    参数:
    num1_str -- 第一个数字字符串
    num2_str -- 第二个数字字符串
    
    返回:
    bool -- 是否为连续的偶数
    """
    try:
        num1 = int(num1_str)
        num2 = int(num2_str)
        
        # 检查是否为偶数
        if num1 % 2 != 0 or num2 % 2 != 0:
            return False
        
        # 检查是否连续（差值为2）
        return abs(num1 - num2) == 2
        
    except (ValueError, TypeError):
        logger.warning(f"无法将字符串转换为数字: {num1_str}, {num2_str}")
        return False 