# 固定比例配套辅料功能说明

## 功能概述

本次更新为屏柜BOM处理系统增加了"固定比例"配套辅料的处理功能。当屏柜BOM中的物料在"物料辅料与把手"文件的"配套辅料"表中匹配到备注为"固定比例"的记录时，系统将自动计算配套辅料的数量并直接填入BOM清单，而不是预留空值。

## 功能特点

1. **自动识别固定比例物料**：系统会检查"配套辅料"表中"备注"列为"固定比例"的物料
2. **精确比例计算**：使用decimal模块进行精确计算，避免浮点数精度问题
3. **自动填入BOM清单**：计算出的配套辅料会直接填入星瀚编码和数量，分母固定为1
4. **保留小数精度**：计算结果保留小数点后两位
5. **向后兼容**：非固定比例的配套辅料仍按原来的方式处理（预留空值）

## 数据结构要求

### "物料辅料与把手"文件的"配套辅料"表需要包含以下列：

- **深瑞星空编码**：主料的编码
- **备注**：标识是否为"固定比例"
- **比例**：物料的比例数值
- **星瀚编码**：配套辅料的星瀚编码

### 数据组织方式：

- 使用合并单元格将同一组配套辅料组织在一起
- 第一行为主料信息（深瑞星空编码、比例）
- 后续行为配套辅料信息（星瀚编码、比例）
- "备注"列合并单元格标注为"固定比例"

## 计算公式

```
配套辅料分子数量 = 主料数量 × (配套辅料比例 / 主料比例)
```

## 使用示例

### 示例1：基本计算
- 屏柜BOM中物料数量：3
- 主料比例：1
- 配套辅料比例：[2, 1, 2]
- 计算结果：[6, 3, 6]

### 示例2：比例换算
- 屏柜BOM中物料数量：3
- 主料比例：3
- 配套辅料比例：1
- 计算结果：1（3 × 1/3 = 1）

### 示例3：小数计算
- 屏柜BOM中物料数量：5
- 主料比例：3
- 配套辅料比例：2
- 计算结果：3.33（5 × 2/3 = 3.33，保留两位小数）

## 技术实现

### 新增函数：

1. **get_fixed_ratio_auxiliary_materials()**
   - 从"物料辅料与把手"文件中获取固定比例配套辅料信息
   - 返回主料编码到配套辅料信息的映射

2. **calculate_fixed_ratio_quantities()**
   - 使用decimal模块进行精确的比例计算
   - 保留小数点后两位

### 修改函数：

1. **get_column_indices_from_auxiliary_sheet()**
   - 扩展为获取四个列的索引：备注、深瑞星空编码、比例、星瀚编码

2. **process_bom_from_design_doc()**
   - 在处理每个物料时检查是否有固定比例配套辅料
   - 优先处理固定比例，其次处理普通配套辅料

## 错误处理

- 当主料比例为0时，系统会输出警告并跳过计算
- 当找不到必要列时，系统会回退到原有逻辑
- 所有异常都会被捕获并记录，不会影响其他功能

## 兼容性

- 完全向后兼容，不影响现有的非固定比例配套辅料处理
- 如果"物料辅料与把手"文件中没有相关列，系统会自动回退到原有逻辑
- 现有的BOM模板和数据格式无需修改
