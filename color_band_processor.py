import pandas as pd
import logging
import math

# 导入日志配置
from logger_config import get_color_band_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

def process_color_band_usage(sleeve_df, sleeve_spec_path):
    """
    处理色带用量统计
    :param sleeve_df: 套管匹配结果 DataFrame
    :param sleeve_spec_path: 套管匹配表路径（包含Sheet2色带规格）
    :return: 色带用量统计 DataFrame
    """
    # 获取色带用量处理日志记录器
    logger = get_color_band_logger()
    logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志
    
    try:
        log_function_start(logger, "process_color_band_usage")
        
        # 1. 读取色带规格表（Sheet2）
        logger.info(f"读取色带规格表: {sleeve_spec_path} - Sheet2")
        color_band_spec = pd.read_excel(sleeve_spec_path, sheet_name='Sheet2')
        logger.info(f"色带规格表字段: {list(color_band_spec.columns)}")
        logger.info(f"色带规格数量: {len(color_band_spec)}")
        
        # 2. 从套管匹配结果中筛选有色带型号的记录
        logger.info("筛选有色带型号的套管记录")
        sleeve_with_band = sleeve_df[
            (sleeve_df['色带型号'].notna()) & 
            (sleeve_df['色带型号'] != '') & 
            (sleeve_df['色带型号'] != '无')
        ].copy()
        
        logger.info(f"有色带需求的套管记录: {len(sleeve_with_band)}条")
        
        if sleeve_with_band.empty:
            logger.warning("没有找到需要色带的套管记录")
            return pd.DataFrame(columns=['屏柜编号', '色带名称', '色带型号', '色带星瀚编码', '色带星空编码', '色带系统分子'])
        
        # 3. 按屏柜编号+色带型号分组，汇总套管长度（使用损耗率后的数量）
        logger.info("按屏柜编号+色带型号分组汇总套管长度（包含损耗率）")

        # 计算包含损耗率的套管长度：套管总长度 × (1 + 损耗率)
        sleeve_with_band['含损耗率长度'] = sleeve_with_band['套管总长度'] * (1 + sleeve_with_band['损耗率'])

        band_usage = sleeve_with_band.groupby(['屏柜编号', '色带型号']).agg({
            '含损耗率长度': 'sum'
        }).reset_index()

        logger.info(f"不同屏柜+色带型号组合数量: {len(band_usage)}")

        # 4. 与色带规格表关联，获取色带详细信息
        logger.info("关联色带规格表获取详细信息")
        result_list = []

        for _, row in band_usage.iterrows():
            cabinet_no = row['屏柜编号']
            band_model = row['色带型号']
            total_length = row['含损耗率长度']
            
            # 在色带规格表中查找匹配的色带
            band_spec = color_band_spec[color_band_spec['色带型号'] == band_model]
            
            if not band_spec.empty:
                spec = band_spec.iloc[0]
                single_roll_length = spec['单卷可印长度/米']
                
                # 计算色带系统分子 = 套管总长度之和 / 色带单卷可印长度/米
                color_band_factor = total_length / single_roll_length
                # 向上取整并保留两位小数
                color_band_factor = math.ceil(color_band_factor * 100) / 100.0
                
                result_list.append({
                    '屏柜编号': cabinet_no,
                    '色带名称': spec['色带名称'],
                    '色带型号': spec['色带型号'],
                    '色带星瀚编码': spec['色带星瀚编码'],
                    '色带星空编码': spec['色带星空编码'],
                    '色带系统分子': color_band_factor
                })
                
                logger.debug(f"屏柜{cabinet_no} 色带 {band_model}: 含损耗率长度 {total_length}米, 单卷长度 {single_roll_length}米, 系统分子 {color_band_factor}")
            else:
                logger.warning(f"色带型号 {band_model} 在色带规格表中未找到匹配记录")
        
        # 5. 生成最终结果
        if result_list:
            result_df = pd.DataFrame(result_list)
            log_function_end(logger, "process_color_band_usage", f"共 {len(result_df)} 条记录")
            return result_df
        else:
            logger.warning("没有生成色带用量统计结果")
            return pd.DataFrame(columns=['屏柜编号', '色带名称', '色带型号', '色带星瀚编码', '色带星空编码', '色带系统分子'])

    except Exception as e:
        log_function_error(logger, "process_color_band_usage", str(e))
        raise