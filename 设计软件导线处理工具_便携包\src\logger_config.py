#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
为每个处理模块创建专门的日志文件，只记录关键节点的运行情况
"""

import logging
import os
from datetime import datetime

def setup_logger(module_name, log_level=logging.INFO):
    """
    为指定模块设置专门的日志记录器
    
    参数:
        module_name: 模块名称，用于创建日志文件
        log_level: 日志级别，默认INFO
    
    返回:
        logger: 配置好的日志记录器
    """
    # 获取当前脚本所在目录（而不是运行目录）
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 在脚本目录下创建logs文件夹
    logs_dir = os.path.join(script_dir, "logs")
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
    
    # 创建日志文件名（在logs文件夹中）
    log_filename = os.path.join(logs_dir, f"{module_name}.log")
    
    # 创建日志记录器
    logger = logging.getLogger(module_name)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    logger.setLevel(log_level)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
    file_handler.setLevel(log_level)
    
    # 创建控制台处理器（只显示WARNING及以上级别）
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    
    # 创建格式化器
    file_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s:%(name)s:%(message)s'
    )
    
    # 设置格式化器
    file_handler.setFormatter(file_formatter)
    console_handler.setFormatter(console_formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def log_function_start(logger, function_name, **kwargs):
    """记录函数开始执行"""
    if kwargs:
        params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        logger.info(f"🚀 开始执行 {function_name}({params})")
    else:
        logger.info(f"🚀 开始执行 {function_name}")

def log_function_end(logger, function_name, result=None):
    """记录函数执行完成"""
    if result is not None:
        logger.info(f"✅ 完成执行 {function_name}，结果: {result}")
    else:
        logger.info(f"✅ 完成执行 {function_name}")

def log_function_error(logger, function_name, error):
    """记录函数执行错误"""
    logger.error(f"❌ 执行 {function_name} 时出错: {str(error)}")

def log_process_step(logger, step_name, details=None):
    """记录处理步骤"""
    if details:
        logger.info(f"📝 {step_name}: {details}")
    else:
        logger.info(f"📝 {step_name}")

def log_data_info(logger, data_name, count=None, details=None):
    """记录数据信息"""
    message = f"📊 {data_name}"
    if count is not None:
        message += f" (共{count}条)"
    if details:
        message += f": {details}"
    logger.info(message)

def log_match_result(logger, match_type, success_count, total_count=None):
    """记录匹配结果"""
    if total_count is not None:
        logger.info(f"🎯 {match_type}匹配完成: {success_count}/{total_count} 成功")
    else:
        logger.info(f"🎯 {match_type}匹配完成: {success_count} 个结果")

def log_file_operation(logger, operation, filename, success=True):
    """记录文件操作"""
    if success:
        logger.info(f"📁 {operation}文件成功: {filename}")
    else:
        logger.error(f"📁 {operation}文件失败: {filename}")

def clear_all_logs():
    """清理logs文件夹下的所有日志文件"""
    # 获取当前脚本所在目录（而不是运行目录）
    script_dir = os.path.dirname(os.path.abspath(__file__))
    logs_dir = os.path.join(script_dir, "logs")
    
    if os.path.exists(logs_dir):
        for filename in os.listdir(logs_dir):
            if filename.endswith('.log'):
                filepath = os.path.join(logs_dir, filename)
                try:
                    os.remove(filepath)
                    # print(f"已删除日志文件: {filepath}")
                except Exception as e:
                    # print(f"删除日志文件 {filepath} 失败: {e}")
                    pass
    else:
        # print(f"logs目录不存在: {logs_dir}")
        pass

# 预定义的模块日志记录器
def get_main_logger():
    """获取主程序日志记录器"""
    return setup_logger("main")

def get_design_doc_logger():
    """获取设计说明书处理日志记录器"""
    return setup_logger("design_doc_processor")

def get_excel_selector_logger():
    """获取Excel文件选择器日志记录器"""
    return setup_logger("excel_file_selector")

def get_terminal_matching_logger():
    """获取终端匹配日志记录器"""
    return setup_logger("terminal_matching")

def get_wire_length_logger():
    """获取导线长度处理日志记录器"""
    return setup_logger("wire_length_processor")

def get_sleeve_matching_logger():
    """获取套管匹配日志记录器"""
    return setup_logger("sleeve_matching")

def get_wire_count_logger():
    """获取导线统计处理日志记录器"""
    return setup_logger("wire_count_processor")

def get_parallel_wire_logger():
    """获取并行导线统计日志记录器"""
    return setup_logger("parallel_wire_statistics")

def get_wire_code_logger():
    """获取线材编码匹配日志记录器"""
    return setup_logger("wire_code_matcher")

def get_preprocess_logger():
    """获取接线表预处理日志记录器"""
    return setup_logger("preprocess_wiring_table")

def get_color_band_logger():
    """获取色带用量处理日志记录器"""
    return setup_logger("color_band_processor")

def get_bom_processor_logger():
    """获取BOM处理日志记录器"""
    return setup_logger("bom_processor")

def get_batch_processor_logger():
    """获取批量处理日志记录器"""
    return setup_logger("batch_processor")

def get_short_wire_logger():
    """获取短接线处理日志记录器"""
    return setup_logger("short_wire_processor")

def get_residual_manager_logger():
    """获取残值管理日志记录器"""
    return setup_logger("residual_manager")

def get_wire_diameter_logger():
    """获取线径匹配日志记录器"""
    return setup_logger("wire_diameter_matcher")

def get_sleeve_matching_fujian_logger():
    """获取福建套管匹配日志记录器"""
    return setup_logger("sleeve_matching_fujian")