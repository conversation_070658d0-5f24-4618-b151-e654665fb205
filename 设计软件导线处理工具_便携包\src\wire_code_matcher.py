import pandas as pd
import logging
import re

# 新增：全局定义小数位数统计函数
def decimal_places(x):
    if pd.isnull(x):
        return None
    s = str(x)
    if '.' in s:
        return len(s.split('.')[-1].rstrip('0'))
    return 0

logger = logging.getLogger('WireCodeMatcher')
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def add_wire_code(wire_count_df, wire_spec_path, material_type, cable_type):
    try:
        # logger.info("开始添加编码列")
        # logger.info(f"材料类型: {material_type}, 线缆类型: {cable_type}")

        # 1. 读取线材规格定义表的所有sheet
        sheet1 = pd.read_excel(wire_spec_path, sheet_name=0)
        sheet2 = pd.read_excel(wire_spec_path, sheet_name=1)
        # 新增：读取Sheet3，构建损耗率和最小有效值映射
        sheet3 = pd.read_excel(wire_spec_path, sheet_name=2)
        loss_rate_map = dict(zip(sheet3['物料编码'], sheet3['损耗率']))
        min_valid_map = dict(zip(sheet3['物料编码'], sheet3['最小有效值']))
        material_name_map = dict(zip(sheet3['物料编码'], sheet3['物料名称']))

        # print('损耗率:', loss_rate_map)
        # print('最小有效值:', min_valid_map)
        # print('物料名称:', material_name_map)
        
        # 新增：统计小数位数
        loss_rate_decimal_map = dict(zip(sheet3['物料编码'], sheet3['损耗率'].apply(decimal_places)))
        min_valid_decimal_map = dict(zip(sheet3['物料编码'], sheet3['最小有效值'].apply(decimal_places)))
        # print('loss_rate_decimal_map:', loss_rate_decimal_map)
        # print('min_valid_decimal_map:', min_valid_decimal_map)
        code_info_dict = {}  # 记录编码对应的损耗率和最小有效值及小数位数
        # logger.debug(f"Sheet1列名: {sheet1.columns.tolist()}")
        # logger.debug(f"Sheet2列名: {sheet2.columns.tolist()}")

        # 2. 创建新列并初始化为空字符串
        wire_count_df['对应编码'] = ''

        # 3. 查找包含线缆类型信息的列
        cable_columns = [col for col in sheet1.columns if re.match(r'.*线材类型.*', str(col))]
        if not cable_columns:
            # logger.error("Sheet1中未找到包含'线材类型'的列")
            return wire_count_df, {}

        cable_type_col = cable_columns[0]
        # logger.info(f"使用Sheet1中的线材类型列: {cable_type_col}")

        # 特殊标识映射列表
        special_identifiers = ['黄(U)', '绿(U)', '红(U)', '蓝(U)', '黄(I)', '绿(I)', '红(I)', '蓝(I)']

        # 4. 匹配编码
        for idx, row in wire_count_df.iterrows():
            wire_color = row['颜色/线径标识']

            # === 新增特殊标识处理逻辑 ===
            if wire_color in special_identifiers:
                # logger.debug(f"处理特殊标识: {wire_color}")

                # 在Sheet2中查找匹配项
                sheet2_match = sheet2[sheet2.iloc[:, 0] == wire_color]

                if not sheet2_match.empty and cable_type in sheet2.columns:
                    mapped_color = sheet2_match[cable_type].values[0]

                    if pd.notna(mapped_color):
                        # logger.debug(f"Sheet2映射成功: {wire_color} -> {mapped_color}")
                        # 使用映射后的标识继续处理
                        wire_color = mapped_color
                    else:
                        logger.warning(f"Sheet2找到匹配但映射值为空: {wire_color}, {cable_type}")
                else:
                    logger.warning(f"Sheet2未找到特殊标识匹配项: {wire_color}, 线缆类型: {cable_type}")
            # === 结束特殊标识处理 ===

            # 处理"未标识"情况
            if wire_color == '未标识':
                special_type = f"{material_type}-未标识"
                # logger.debug(f"处理未标识: {special_type}")

                # 在Sheet2中查找匹配项
                match = sheet2[
                    (sheet2.iloc[:, 0] == special_type)  # 第一列匹配特殊类型
                ]

                if not match.empty and cable_type in match.columns:
                    code = match[cable_type].values[0]
                    if pd.notna(code):
                        wire_count_df.at[idx, '对应编码'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
                        logger.debug(f"Sheet2匹配成功: {special_type} -> {code}")
                    else:
                        logger.warning(f"Sheet2找到匹配但编码为空: {special_type}, {cable_type}")
                else:
                    logger.warning(f"Sheet2未找到匹配项: {special_type}, 线缆类型: {cable_type}")

            else:
                # 在Sheet1中查找匹配项
                match = sheet1[
                    (sheet1[cable_type_col] == wire_color) |
                    (sheet1['线材类型'] == wire_color)
                    ]

                if not match.empty and material_type in match.columns:
                    code = match[material_type].values[0]
                    if pd.notna(code):
                        wire_count_df.at[idx, '对应编码'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
                        logger.debug(f"Sheet1匹配成功: {wire_color} -> {code}")
                    else:
                        logger.warning(f"Sheet1找到匹配但编码为空: {wire_color}")
                else:
                    logger.warning(f"Sheet1未找到匹配项: {wire_color}")

        logger.info("编码列添加完成")
        return wire_count_df, code_info_dict

    except Exception as e:
        logger.exception("添加编码列时发生错误")
        raise


def match_printer_wire_code(printer_df, wire_spec_path, material_type):
    """为打印机数据线匹配对应编码并分组显示设备标号"""
    try:
        if printer_df.empty:
            return printer_df

        # logger.info("开始为打印机数据线匹配编码")

        # 读取线材规格定义表
        sheet1 = pd.read_excel(wire_spec_path, sheet_name=0)

        # 创建新列
        printer_df['固定长度'] = 2.5  # 固定长度为2.5米
        printer_df['对应编码'] = ''
        printer_df['对应线径'] = ''  # 新增对应线径列

        # 步骤1: 先完成编码匹配和线径匹配
        for idx, row in printer_df.iterrows():
            wire_type = row['导线种类']

            # 在Sheet1中查找匹配项
            match = sheet1[sheet1['线材类型'] == wire_type]

            if not match.empty and material_type in match.columns:
                code = match[material_type].values[0]
                if pd.notna(code):
                    printer_df.at[idx, '对应编码'] = code
                    logger.debug(f"打印机数据线匹配成功: {wire_type} -> {code}")
                else:
                    logger.warning(f"找到匹配但编码为空: {wire_type}")
            else:
                logger.warning(f"未找到匹配项: {wire_type}")

            # 匹配对应线径
            if not match.empty and '对应线径' in match.columns:
                diameter = match['对应线径'].values[0]
                if pd.notna(diameter):
                    printer_df.at[idx, '对应线径'] = diameter
                    logger.debug(f"打印机数据线线径匹配成功: {wire_type} -> {diameter}")
                else:
                    logger.warning(f"找到匹配但线径为空: {wire_type}")
            else:
                logger.warning(f"未找到线径匹配项: {wire_type}")

        logger.info("编码匹配完成，开始分组合并设备标号")

        # 步骤2: 转换导线起点和导线终点
        def transform_start_point(point):
            if not isinstance(point, str):
                return point

            # 提取设备标号（第一个冒号前）
            device = point.split(':')[0]

            # 提取连接点名称（第一个斜杠后的部分，不包含端口信息）
            parts = point.split('/')
            if len(parts) > 1:
                connection = parts[-1].split(':')[0]
                return f"{device}/{connection}"
            return device

        def transform_end_point(point):
            if not isinstance(point, str):
                return point

            # 提取连接点名称（第一个冒号前）
            connection = point.split(':')[0]

            # 提取设备标号（最后一个斜杠前的部分）
            parts = point.split('/')
            if len(parts) > 1:
                device = parts[0].split(':')[0]
                return f"{connection}/{device}"
            return connection

        printer_df['导线起点'] = printer_df['导线起点'].apply(transform_start_point)
        printer_df['导线终点'] = printer_df['导线终点'].apply(transform_end_point)

        # 提取设备标号（用于分组）
        printer_df['设备标号'] = printer_df['导线起点'].apply(
            lambda x: x.split('/')[0] if isinstance(x, str) and '/' in x else x
        )

        # 分组处理（保留需要的列）- 按屏柜编号和设备标号分组，确保不同屏柜不合并
        grouped_df = printer_df.groupby(['屏柜编号', '设备标号'], as_index=False).agg({
            '设备类型（起点/终点）': 'first',
            '导线起点': 'first',
            '导线终点': 'first',
            '导线种类': 'first',
            '固定长度': 'first',
            '对应编码': 'first',
            '对应线径': 'first',  # 新增对应线径列
            '设备标号': 'count'  # 计数用于排序
        }).rename(columns={'设备标号': '根数'})  # 重命名计数列为根数

        # 按屏柜编号排序（编号小的排在前面）
        grouped_df = grouped_df.sort_values('屏柜编号', ascending=True)

        # 重置索引并选择需要的列
        result_df = grouped_df[[
            '屏柜编号', '设备类型（起点/终点）', '导线起点', '导线终点',
            '导线种类', '对应线径', '固定长度', '对应编码', '根数'
        ]].reset_index(drop=True)

        logger.info("打印机数据线编码匹配和分组显示完成")
        return result_df

    except Exception as e:
        logger.exception("为打印机数据线匹配编码时发生错误")
        return printer_df


def add_parallel_wire_code(parallel_df, wire_spec_path, material_type, cable_type):
    """为并线统计表添加导线1和导线2的编码，处理特殊标识和多芯线情况"""
    try:
        logger.info("开始为并线统计表添加编码...")

        # 读取线材规格定义表
        sheet1 = pd.read_excel(wire_spec_path, sheet_name=0)
        sheet2 = pd.read_excel(wire_spec_path, sheet_name=1)
        # 新增：读取Sheet3，构建损耗率和最小有效值映射
        sheet3 = pd.read_excel(wire_spec_path, sheet_name=2)
        loss_rate_map = dict(zip(sheet3['物料编码'], sheet3['损耗率']))
        min_valid_map = dict(zip(sheet3['物料编码'], sheet3['最小有效值']))
        material_name_map = dict(zip(sheet3['物料编码'], sheet3['物料名称']))
        # 新增：统计小数位数
        loss_rate_decimal_map = dict(zip(sheet3['物料编码'], sheet3['损耗率'].apply(decimal_places)))
        min_valid_decimal_map = dict(zip(sheet3['物料编码'], sheet3['最小有效值'].apply(decimal_places)))
        # print('loss_rate_decimal_map:', loss_rate_decimal_map)
        # print('min_valid_decimal_map:', min_valid_decimal_map)
        code_info_dict = {}  # 记录编码对应的损耗率和最小有效值及小数位数

        # 创建新的编码列
        parallel_df['对应编码1'] = ''
        parallel_df['对应编码2'] = ''

        # 特殊标识映射列表
        special_identifiers = ['黄(U)', '绿(U)', '红(U)', '蓝(U)', '黄(I)', '绿(I)', '红(I)', '蓝(I)']
        multi_core_identifiers = ['棕($)', '蓝($)', '黑($)']

        # 创建分组键（并线组号去掉冒号右侧部分）
        parallel_df['分组键'] = parallel_df['并线组号'].str.split(':').str[0]

        # 存储多芯线类型（两芯线或四芯线）
        multi_core_types = {}

        # 第一遍：识别多芯线组并确定线材类型
        for group_key, group in parallel_df.groupby('分组键'):
            # 收集组内所有颜色标识
            all_colors = []
            for idx, row in group.iterrows():
                all_colors.append(str(row['颜色/线径标识1']).strip())
                all_colors.append(str(row['颜色/线径标识2']).strip())

            # 检查多芯线特殊标识
            has_brown = any('棕($)' in color for color in all_colors)
            has_blue = any('蓝($)' in color for color in all_colors)
            has_black = any('黑($)' in color for color in all_colors)

            # 确定线材类型
            if has_brown and has_blue and has_black:
                multi_core_types[group_key] = "四芯线"
            elif has_brown and has_blue and not has_black:
                multi_core_types[group_key] = "两芯线"

        # 第二遍：处理编码
        for idx, row in parallel_df.iterrows():
            group_key = row['分组键']

            # 处理导线1
            wire_color1 = str(row['颜色/线径标识1']).strip()

            # 检查是否是多芯线组
            if group_key in multi_core_types:
                wire_type = multi_core_types[group_key]
                # 在Sheet1中查找匹配
                match = sheet1[sheet1['线材类型'] == wire_type]
                if not match.empty and material_type in match.columns:
                    code = match[material_type].values[0]
                    if pd.notna(code):
                        parallel_df.at[idx, '对应编码1'] = code
                        parallel_df.at[idx, '对应编码2'] = code  # 同一组使用相同编码
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
                        continue  # 跳过后续处理

            # 处理特殊线类型映射
            if wire_color1 in special_identifiers:
                # 在Sheet2中查找匹配
                sheet2_match = sheet2[sheet2.iloc[:, 0] == wire_color1]
                if not sheet2_match.empty and cable_type in sheet2.columns:
                    mapped_color = sheet2_match[cable_type].values[0]
                    if pd.notna(mapped_color):
                        wire_color1 = mapped_color

            # 处理"未标识"情况
            if wire_color1 == '未标识':
                special_type = f"{material_type}-未标识"
                # 在Sheet2中查找匹配
                sheet2_match = sheet2[sheet2.iloc[:, 0] == special_type]
                if not sheet2_match.empty and cable_type in sheet2.columns:
                    code = sheet2_match[cable_type].values[0]
                    if pd.notna(code):
                        parallel_df.at[idx, '对应编码1'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
            # 正常线材类型匹配
            else:
                match = sheet1[sheet1['线材类型'] == wire_color1]
                if not match.empty and material_type in match.columns:
                    code = match[material_type].values[0]
                    if pd.notna(code):
                        parallel_df.at[idx, '对应编码1'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
            # 处理导线2（如果组不是多芯线组）
            wire_color2 = str(row['颜色/线径标识2']).strip()

            # 处理特殊线类型映射
            if wire_color2 in special_identifiers:
                # 在Sheet2中查找匹配
                sheet2_match = sheet2[sheet2.iloc[:, 0] == wire_color2]
                if not sheet2_match.empty and cable_type in sheet2.columns:
                    mapped_color = sheet2_match[cable_type].values[0]
                    if pd.notna(mapped_color):
                        wire_color2 = mapped_color

            # 处理"未标识"情况
            if wire_color2 == '未标识':
                special_type = f"{material_type}-未标识"
                # 在Sheet2中查找匹配
                sheet2_match = sheet2[sheet2.iloc[:, 0] == special_type]
                if not sheet2_match.empty and cable_type in sheet2.columns:
                    code = sheet2_match[cable_type].values[0]
                    if pd.notna(code):
                        parallel_df.at[idx, '对应编码2'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
            # 正常线材类型匹配
            else:
                match = sheet1[sheet1['线材类型'] == wire_color2]
                if not match.empty and material_type in match.columns:
                    code = match[material_type].values[0]
                    if pd.notna(code):
                        parallel_df.at[idx, '对应编码2'] = code
                        # 新增：记录损耗率和最小有效值
                        code_info_dict[code] = {
                            '损耗率': loss_rate_map.get(code),
                            '最小有效值': min_valid_map.get(code),
                            '损耗率小数位数': loss_rate_decimal_map.get(code),
                            '最小有效值小数位数': min_valid_decimal_map.get(code),
                            '物料名称': material_name_map.get(code)
                        }
                        # 输出记录的信息到终端
                        # print(f"记录物料信息 - 编码: {code}")
                        # print(f"  损耗率: {loss_rate_map.get(code)}")
                        # print(f"  最小有效值: {min_valid_map.get(code)}")
                        # print(f"  损耗率小数位数: {loss_rate_decimal_map.get(code)}")
                        # print(f"  最小有效值小数位数: {min_valid_decimal_map.get(code)}")
                        # print(f"  物料名称: {material_name_map.get(code)}")
                        # print("-" * 50)
        # 清理临时列
        parallel_df = parallel_df.drop(columns=['分组键'])

        logger.info("并线统计表编码添加完成")
        return parallel_df, code_info_dict

    except Exception as e:
        logger.exception("为并线统计表添加编码时发生错误")
        return parallel_df, {}


def add_power_wire_code(power_wire_df, wire_spec_path, material_type):
    """为电源线记录添加对应编码"""
    try:
        logger.info("开始为电源线记录添加编码...")

        # 读取线材规格定义表
        sheet1 = pd.read_excel(wire_spec_path, sheet_name=0)

        # 创建新列
        power_wire_df['对应编码'] = ''

        # 遍历每一行
        for idx, row in power_wire_df.iterrows():
            # 检查设备类型是否包含"电源线"
            device_type = str(row['设备类型（起点/终点）']).lower()
            if '电源线' in device_type:
                # 在Sheet1中查找"电源线"对应的编码
                match = sheet1[sheet1['线材类型'] == '电源线']

                if not match.empty and material_type in match.columns:
                    code = match[material_type].values[0]
                    if pd.notna(code):
                        power_wire_df.at[idx, '对应编码'] = code

        logger.info("电源线记录编码添加完成")
        return power_wire_df

    except Exception as e:
        logger.exception("为电源线记录添加编码时发生错误")
        return power_wire_df