import pandas as pd
import logging

logger = logging.getLogger('WireCountProcessor')


def add_wire_diameter(wire_count_df, wire_spec_def, material_type, cable_type):
    """
    为导线统计结果添加对应线径（增加特殊线判断逻辑）
    :param wire_count_df: 导线统计结果DataFrame
    :param wire_spec_def: 线材规格定义文件路径
    :param material_type: 材料类型（普通型/交联聚乙烯/自定义线材）
    :param cable_type: 线缆类型（G/X/N/Z）
    :return: 添加了对应线径列的DataFrame
    """
    try:
        # 读取线材规格定义表Sheet1和Sheet2
        spec_df_sheet1 = pd.read_excel(wire_spec_def, sheet_name='Sheet1')
        spec_df_sheet2 = pd.read_excel(wire_spec_def, sheet_name='Sheet2')

        # 创建线材类型->线径的映射字典（Sheet1）
        diameter_map = {}
        for _, row in spec_df_sheet1.iterrows():
            wire_type = row['线材类型']
            diameter = row['对应线径']
            if pd.notna(wire_type) and pd.notna(diameter):
                diameter_map[str(wire_type).strip()] = diameter

        # 创建特殊线映射字典（Sheet2）
        special_mapping = {}
        special_types = ['黄(U)', '绿(U)', '红(U)', '蓝(U)', '黄(I)', '绿(I)', '红(I)', '蓝(I)']

        for _, row in spec_df_sheet2.iterrows():
            wire_type = str(row['线材类型']).strip()
            if wire_type in special_types:
                # 获取对应线缆类型的映射值
                mapped_value = str(row[cable_type]).strip()
                if mapped_value:
                    special_mapping[wire_type] = mapped_value

        # 处理未标识的特殊规则
        unid_rules = {
            ('G'): 1.5,
            ('X', 'N', 'Z'): 1.0
        }

        # 遍历每一行并填充对应线径
        for idx, row in wire_count_df.iterrows():
            original_wire_type = str(row['颜色/线径标识']).strip()
            wire_type = original_wire_type

            # 处理特殊线类型（黄(U)、绿(U)等）
            if wire_type in special_mapping:
                # 使用映射后的新线材类型
                wire_type = special_mapping[original_wire_type]
                logger.debug(f"特殊线映射: {original_wire_type} -> {wire_type}")

            # 处理未标识的情况
            if wire_type == '未标识':
                for cable_types, diameter in unid_rules.items():
                    if cable_type in cable_types:
                        wire_count_df.at[idx, '对应线径'] = diameter
                        break

            # 正常线材类型匹配
            elif wire_type in diameter_map:
                wire_count_df.at[idx, '对应线径'] = diameter_map[wire_type]

            # 未匹配到的情况
            else:
                logger.warning(f"未找到线材类型匹配: {original_wire_type} (映射后: {wire_type})")
                wire_count_df.at[idx, '对应线径'] = None

        return wire_count_df

    except Exception as e:
        logger.exception("添加线径信息时发生错误")
        raise