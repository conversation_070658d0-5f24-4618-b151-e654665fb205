import pandas as pd
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ParallelWireStatistics')
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def find_parallel_wires(df):
    """
    在屏柜配线套管数据中查找并接导线

    参数:
    df -- 包含屏柜配线套管数据的DataFrame，需包含列：'屏柜编号', '导线起点', '导线终点', '颜色/线径标识'

    返回:
    包含并接导线统计结果的DataFrame，新增设备类型字段
    """
    logger.info("开始查找并接导线...")
    logger.info(f"输入数据行数: {len(df)}")

    # 检查关键列是否存在
    required_columns = ['屏柜编号', '导线起点', '导线终点', '颜色/线径标识', '设备类型（起点/终点）']

    # 检查列名是否存在，处理可能的空格或格式问题
    actual_columns = df.columns.tolist()
    logger.debug(f"输入数据列名: {actual_columns}")

    # 尝试匹配列名
    column_mapping = {}
    for col in required_columns:
        # 尝试精确匹配
        if col in actual_columns:
            column_mapping[col] = col
        else:
            # 尝试模糊匹配（忽略空格）
            matched = False
            for actual_col in actual_columns:
                if col.replace(" ", "") == actual_col.replace(" ", ""):
                    column_mapping[col] = actual_col
                    logger.warning(f"使用模糊匹配: '{col}' -> '{actual_col}'")
                    matched = True
                    break
            if not matched:
                logger.error(f"无法匹配列: {col}")

    # 检查是否所有列都匹配成功
    missing = [col for col in required_columns if col not in column_mapping]
    if missing:
        logger.error(f"缺少必要列: {missing}")
        raise ValueError(f"输入DataFrame缺少必要列: {missing}")

    # 重命名列以确保一致性
    for standard_col, actual_col in column_mapping.items():
        if standard_col != actual_col:
            df = df.rename(columns={actual_col: standard_col})
            logger.info(f"重命名列: '{actual_col}' -> '{standard_col}'")

    # 筛选有效数据（排除GND和屏蔽层）
    valid_df = df[
        (~df['导线起点'].isin(['GND', '屏蔽层'])) &
        (~df['导线终点'].isin(['GND', '屏蔽层']))
        ].copy()

    logger.info(f"排除GND和屏蔽层后数据行数: {len(valid_df)}")

    # 创建连接点字典（排除GND和屏蔽层）
    connection_dict = {}
    for idx, row in valid_df.iterrows():
        points = set(row['导线起点'].split('/'))
        points = points.union(row['导线终点'].split('/'))

        for point in points:
            if point not in ['GND', '屏蔽层']:
                connection_dict.setdefault(point, []).append({
                    'cabinet': row['屏柜编号'],
                    'wire_start': row['导线起点'],
                    'wire_end': row['导线终点'],
                    'color': row['颜色/线径标识'],
                    'device_type': row['设备类型（起点/终点）'],  # 新增设备类型字段
                    'index': idx  # 记录原始索引
                })

    # 识别并线对
    parallel_pairs = []
    seen_pairs = set()  # 避免重复记录

    for point, records in connection_dict.items():
        if len(records) >= 2:  # 至少两条线共享连接点
            # 按屏柜分组
            cabinet_groups = {}
            for record in records:
                cabinet_groups.setdefault(record['cabinet'], []).append(record)

            # 在每个屏柜内查找并线
            for cabinet, wires in cabinet_groups.items():
                if len(wires) >= 2:
                    # 生成所有可能的导线对（避免重复）
                    for i in range(len(wires)):
                        for j in range(i + 1, len(wires)):
                            wire1 = wires[i]
                            wire2 = wires[j]

                            # 创建唯一标识键，避免重复
                            pair_key = tuple(sorted([
                                (wire1['wire_start'], wire1['wire_end'], wire1['index']),
                                (wire2['wire_start'], wire2['wire_end'], wire2['index'])
                            ]))

                            if pair_key not in seen_pairs:
                                seen_pairs.add(pair_key)
                                parallel_pairs.append({
                                    '并线组号': f"{cabinet}_{point}",
                                    '导线1起点': wire1['wire_start'],
                                    '导线1终点': wire1['wire_end'],
                                    '导线2起点': wire2['wire_start'],
                                    '导线2终点': wire2['wire_end'],
                                    '屏柜号': cabinet,
                                    '颜色/线径标识1': wire1['color'],
                                    '颜色/线径标识2': wire2['color'],
                                    '设备类型1': wire1['device_type'],  # 新增设备类型1
                                    '设备类型2': wire2['device_type'],  # 新增设备类型2
                                    '索引1': wire1['index'],
                                    '索引2': wire2['index']
                                })

    # 创建结果DataFrame
    result_columns = [
        '并线组号', '导线1起点', '导线1终点', '导线2起点', '导线2终点',
        '屏柜号', '颜色/线径标识1', '颜色/线径标识2',
        '设备类型1', '设备类型2',  # 新增设备类型字段
        '索引1', '索引2'
    ]
    result_df = pd.DataFrame(parallel_pairs, columns=result_columns)

    logger.info(f"找到并线对数量: {len(result_df)}")
    return result_df


def process_special_wires_in_parallel(parallel_df, wire_spec_def, material_type, cable_type):
    """
    处理并线统计表中的特殊线材标识（蓝($)、棕($)、黑($)）和空值，添加对应线径，并处理多芯线单根长度置零。
    """
    import numpy as np
    # ==== 新增：提前读取线材规格定义表 ====
    spec_df_sheet1 = pd.read_excel(wire_spec_def, sheet_name='Sheet1')

    # 创建线材类型->线径的映射字典
    diameter_map = {}
    for _, row in spec_df_sheet1.iterrows():
        wire_type = row['线材类型']
        diameter = row['对应线径']
        if pd.notna(wire_type) and pd.notna(diameter):
            diameter_map[str(wire_type).strip()] = diameter

    # 添加新列用于存储线径
    parallel_df['对应线径1'] = None
    parallel_df['对应线径2'] = None

    # 提取分组键（冒号左侧部分）
    parallel_df['分组键'] = parallel_df['并线组号'].str.split(':').str[0]

    # 多芯线颜色标识
    multi_core_colors = ['棕($)', '蓝($)', '黑($)']

    # 按分组键分组处理
    grouped = parallel_df.groupby('分组键')

    for group_key, group_df in grouped:
        # 收集组内所有颜色标识
        all_colors = []
        for idx, row in group_df.iterrows():
            all_colors.append(str(row['颜色/线径标识1']))
            all_colors.append(str(row['颜色/线径标识2']))

        # 原有线径处理逻辑...
        has_blue = any('蓝($)' in color for color in all_colors)
        has_brown = any('棕($)' in color for color in all_colors)
        has_black = any('黑($)' in color for color in all_colors)

        # 确定线材类型
        if has_blue and has_brown and has_black:
            wire_type = "四芯线"
            diameter = diameter_map.get(wire_type)  # 从线材规格定义表获取
        elif has_blue and has_brown and not has_black:
            wire_type = "两芯线"
            diameter = diameter_map.get(wire_type)  # 从线材规格定义表获取
        else:
            continue  # 不符合条件，跳过

        # 更新组内所有记录的线径
        if diameter is not None:
            indices = group_df.index
            parallel_df.loc[indices, '对应线径1'] = diameter
            parallel_df.loc[indices, '对应线径2'] = diameter
        else:
            logger.warning(f"线材类型 '{wire_type}' 未在线材规格定义表中找到")

    # 清理临时列
    parallel_df = parallel_df.drop(columns=['分组键'])

    # 读取线材规格定义表
    spec_df_sheet1 = pd.read_excel(wire_spec_def, sheet_name='Sheet1')
    spec_df_sheet2 = pd.read_excel(wire_spec_def, sheet_name='Sheet2')

    # 创建线材类型->线径的映射字典（Sheet1）
    diameter_map = {}
    for _, row in spec_df_sheet1.iterrows():
        wire_type = row['线材类型']
        diameter = row['对应线径']
        if pd.notna(wire_type) and pd.notna(diameter):
            diameter_map[str(wire_type).strip()] = diameter

    # 创建特殊线映射字典（Sheet2）
    special_mapping = {}
    special_types = ['黄(U)', '绿(U)', '红(U)', '蓝(U)', '黄(I)', '绿(I)', '红(I)', '蓝(I)']

    for _, row in spec_df_sheet2.iterrows():
        wire_type = str(row['线材类型']).strip()
        if wire_type in special_types:
            # 获取对应线缆类型的映射值
            mapped_value = str(row[cable_type]).strip()
            if mapped_value:
                special_mapping[wire_type] = mapped_value

    # 处理未标识的特殊规则
    unid_rules = {
        ('G'): 1.5,
        ('X', 'N', 'Z'): 1.0
    }

    # 处理空值并添加线径
    for idx, row in parallel_df.iterrows():
        # 处理第一列颜色/线径标识
        wire_type1 = str(row['颜色/线径标识1']).strip()

        # 处理空值
        if wire_type1 == 'nan' or wire_type1 == '':
            d_value = row.get('设备类型1', '')
            d_value_str = str(d_value).lower()  # 转换为小写以便匹配
            if '屏蔽层' in d_value_str:
                wire_type1 = '屏蔽层'
            elif '接地铜排' in str(d_value):
                wire_type1 = '花(**)'
            else:
                wire_type1 = '未标识'
            parallel_df.at[idx, '颜色/线径标识1'] = wire_type1

        # ==== 修改点1：如果颜色标识是"屏蔽层"，则填入"屏蔽层" ====
        if wire_type1 == '屏蔽层':
            parallel_df.at[idx, '对应线径1'] = '屏蔽层'
        else:
            # 处理特殊线类型
            if wire_type1 in special_mapping:
                wire_type1 = special_mapping[wire_type1]

            # 处理未标识
            if wire_type1 == '未标识':
                for cable_types, diameter in unid_rules.items():
                    if cable_type in cable_types:
                        parallel_df.at[idx, '对应线径1'] = diameter
                        break
            # 正常线材类型匹配
            elif wire_type1 in diameter_map:
                parallel_df.at[idx, '对应线径1'] = diameter_map[wire_type1]

        # 处理第二列颜色/线径标识
        wire_type2 = str(row['颜色/线径标识2']).strip()

        # 处理空值
        if wire_type2 == 'nan' or wire_type2 == '':
            d_value = row.get('设备类型2', '')
            d_value_str = str(d_value).lower()  # 转换为小写以便匹配
            if '屏蔽层' in d_value_str:
                wire_type2 = '屏蔽层'
            elif '接地铜排' in str(d_value):
                wire_type2 = '花(**)'
            else:
                wire_type2 = '未标识'
            parallel_df.at[idx, '颜色/线径标识2'] = wire_type2

        # ==== 修改点2：如果颜色标识是"屏蔽层"，则填入"屏蔽层" ====
        if wire_type2 == '屏蔽层':
            parallel_df.at[idx, '对应线径2'] = '屏蔽层'
        else:
            # 处理特殊线类型
            if wire_type2 in special_mapping:
                wire_type2 = special_mapping[wire_type2]

            # 处理未标识
            if wire_type2 == '未标识':
                for cable_types, diameter in unid_rules.items():
                    if cable_type in cable_types:
                        parallel_df.at[idx, '对应线径2'] = diameter
                        break
            # 正常线材类型匹配
            elif wire_type2 in diameter_map:
                parallel_df.at[idx, '对应线径2'] = diameter_map[wire_type2]

    return parallel_df